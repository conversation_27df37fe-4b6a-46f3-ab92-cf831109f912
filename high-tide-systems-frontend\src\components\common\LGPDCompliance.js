'use client';

import Link from 'next/link';
import { Shield, ExternalLink, Info } from 'lucide-react';

const LGPDCompliance = ({ variant = 'banner', className = '' }) => {
  if (variant === 'banner') {
    return (
      <div className={`bg-primary-50 dark:bg-primary-900/20 border border-primary-200 dark:border-primary-700 rounded-lg p-4 ${className}`}>
        <div className="flex items-start space-x-3">
          <Shield className="text-primary-600 mt-1 flex-shrink-0" size={20} />
          <div className="flex-1">
            <h4 className="text-sm font-semibold text-primary-900 dark:text-primary-100 mb-1">
              🛡️ Proteção de Dados Garantida
            </h4>
            <p className="text-xs text-primary-800 dark:text-primary-200 mb-2">
              Seus dados estão protegidos conforme a Lei Geral de Proteção de Dados (LGPD).
            </p>
            <div className="flex flex-wrap gap-2 text-xs">
              <Link 
                href="/legal/politica-privacidade" 
                className="text-primary-700 dark:text-primary-300 hover:text-primary-800 dark:hover:text-primary-200 underline inline-flex items-center"
              >
                Política de Privacidade
                <ExternalLink size={10} className="ml-1" />
              </Link>
              <Link 
                href="/legal/exercer-direitos" 
                className="text-primary-700 dark:text-primary-300 hover:text-primary-800 dark:hover:text-primary-200 underline inline-flex items-center"
              >
                Exercer Direitos
                <ExternalLink size={10} className="ml-1" />
              </Link>
            </div>
          </div>
        </div>
      </div>
    );
  }

  if (variant === 'footer') {
    return (
      <div className={`text-center py-4 border-t border-gray-200 dark:border-gray-700 ${className}`}>
        <div className="flex items-center justify-center space-x-2 text-xs text-gray-500 dark:text-gray-400 mb-2">
          <Shield size={14} />
          <span>Conformidade LGPD</span>
        </div>
        <p className="text-xs text-gray-400 dark:text-gray-500 mb-2">
          Sistema em conformidade com a Lei nº 13.709/2018 (LGPD)
        </p>
        <div className="flex justify-center space-x-4 text-xs">
          <Link 
            href="/legal/politica-privacidade" 
            className="text-gray-500 dark:text-gray-400 hover:text-primary-600 dark:hover:text-primary-400 underline"
          >
            Privacidade
          </Link>
          <Link 
            href="/legal/termos-uso" 
            className="text-gray-500 dark:text-gray-400 hover:text-primary-600 dark:hover:text-primary-400 underline"
          >
            Termos
          </Link>
          <Link 
            href="/legal/exercer-direitos" 
            className="text-gray-500 dark:text-gray-400 hover:text-primary-600 dark:hover:text-primary-400 underline"
          >
            Direitos LGPD
          </Link>
        </div>
      </div>
    );
  }

  if (variant === 'compact') {
    return (
      <div className={`flex items-center space-x-2 text-xs text-gray-500 dark:text-gray-400 ${className}`}>
        <Shield size={12} />
        <span>Protegido pela LGPD</span>
        <Link 
          href="/legal/politica-privacidade" 
          className="text-primary-600 dark:text-primary-400 hover:underline"
        >
          Saiba mais
        </Link>
      </div>
    );
  }

  if (variant === 'modal') {
    return (
      <div className={`bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-700 rounded-lg p-4 ${className}`}>
        <div className="flex items-start space-x-3">
          <Info className="text-blue-600 mt-1 flex-shrink-0" size={20} />
          <div>
            <h4 className="text-sm font-semibold text-blue-900 dark:text-blue-100 mb-2">
              Informações sobre Tratamento de Dados
            </h4>
            <p className="text-xs text-blue-800 dark:text-blue-200 mb-3">
              Ao utilizar este sistema, você concorda com o tratamento de seus dados pessoais 
              conforme nossa Política de Privacidade e em conformidade com a LGPD.
            </p>
            <div className="flex flex-wrap gap-2 text-xs">
              <Link 
                href="/legal/politica-privacidade" 
                target="_blank"
                className="inline-flex items-center px-2 py-1 bg-blue-600 text-white rounded hover:bg-blue-700 transition-colors"
              >
                Política de Privacidade
                <ExternalLink size={10} className="ml-1" />
              </Link>
              <Link 
                href="/legal/exercer-direitos" 
                target="_blank"
                className="inline-flex items-center px-2 py-1 border border-blue-300 dark:border-blue-600 text-blue-700 dark:text-blue-300 rounded hover:bg-blue-50 dark:hover:bg-blue-900/20 transition-colors"
              >
                Exercer Direitos
                <ExternalLink size={10} className="ml-1" />
              </Link>
            </div>
          </div>
        </div>
      </div>
    );
  }

  // Default variant
  return (
    <div className={`bg-gray-50 dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-lg p-3 ${className}`}>
      <div className="flex items-center space-x-2 text-sm text-gray-700 dark:text-gray-300">
        <Shield size={16} className="text-gray-500 dark:text-gray-400" />
        <span>Dados protegidos pela LGPD</span>
        <Link 
          href="/legal/politica-privacidade" 
          className="text-primary-600 dark:text-primary-400 hover:underline ml-auto"
        >
          Ver política
        </Link>
      </div>
    </div>
  );
};

export default LGPDCompliance;
