!function(e,t){"object"==typeof exports&&"undefined"!=typeof module?t(exports,require("react"),require("react-dom"),require("use-sync-external-store/shim"),require("use-sync-external-store/shim/with-selector")):"function"==typeof define&&define.amd?define(["exports","react","react-dom","use-sync-external-store/shim","use-sync-external-store/shim/with-selector"],t):t((e="undefined"!=typeof globalThis?globalThis:e||self).ReactBeautifulDnd={},e.React,e.ReactDOM,e.shim)}(this,(function(e,t,r,n){"use strict";function o(e){var t=Object.create(null);return e&&Object.keys(e).forEach((function(r){if("default"!==r){var n=Object.getOwnPropertyDescriptor(e,r);Object.defineProperty(t,r,n.get?n:{enumerable:!0,get:function(){return e[r]}})}})),t.default=e,Object.freeze(t)}var i=o(t);function a(){}function s(e,t,r){const n=t.map((t=>{const n=(o=r,i=t.options,{...o,...i});var o,i;return e.addEventListener(t.eventName,t.fn,n),function(){e.removeEventListener(t.eventName,t.fn,n)}}));return function(){n.forEach((e=>{e()}))}}const l=!0,c="Invariant failed";class d extends Error{}function u(e,t){if(!e)throw new d(l?c:`${c}: ${t||""}`)}d.prototype.toString=function(){return this.message};class p extends t.Component{constructor(...e){super(...e),this.callbacks=null,this.unbind=a,this.onWindowError=e=>{const t=this.getCallbacks();t.isDragging()&&t.tryAbort();e.error instanceof d&&e.preventDefault()},this.getCallbacks=()=>{if(!this.callbacks)throw new Error("Unable to find AppCallbacks in <ErrorBoundary/>");return this.callbacks},this.setCallbacks=e=>{this.callbacks=e}}componentDidMount(){this.unbind=s(window,[{eventName:"error",fn:this.onWindowError}])}componentDidCatch(e){if(!(e instanceof d))throw e;this.setState({})}componentWillUnmount(){this.unbind()}render(){return this.props.children(this.setCallbacks)}}const f=e=>e+1,g=(e,t)=>{const r=e.droppableId===t.droppableId,n=f(e.index),o=f(t.index);return r?`\n      You have moved the item from position ${n}\n      to position ${o}\n    `:`\n    You have moved the item from position ${n}\n    in list ${e.droppableId}\n    to list ${t.droppableId}\n    in position ${o}\n  `},m=(e,t,r)=>t.droppableId===r.droppableId?`\n      The item ${e}\n      has been combined with ${r.draggableId}`:`\n      The item ${e}\n      in list ${t.droppableId}\n      has been combined with ${r.draggableId}\n      in list ${r.droppableId}\n    `,b=e=>`\n  The item has returned to its starting position\n  of ${f(e.index)}\n`,h={dragHandleUsageInstructions:"\n  Press space bar to start a drag.\n  When dragging you can use the arrow keys to move the item around and escape to cancel.\n  Some screen readers may require you to be in focus mode or to use your pass through key\n",onDragStart:e=>`\n  You have lifted an item in position ${f(e.source.index)}\n`,onDragUpdate:e=>{const t=e.destination;if(t)return g(e.source,t);const r=e.combine;return r?m(e.draggableId,e.source,r):"You are over an area that cannot be dropped on"},onDragEnd:e=>{if("CANCEL"===e.reason)return`\n      Movement cancelled.\n      ${b(e.source)}\n    `;const t=e.destination,r=e.combine;return t?`\n      You have dropped the item.\n      ${g(e.source,t)}\n    `:r?`\n      You have dropped the item.\n      ${m(e.draggableId,e.source,r)}\n    `:`\n    The item has been dropped while not over a drop area.\n    ${b(e.source)}\n  `}};var y=h;function v(e){return v="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},v(e)}function x(e){var t=function(e,t){if("object"!=v(e)||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=v(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==v(t)?t:t+""}function I(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),r.push.apply(r,n)}return r}function D(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?I(Object(r),!0).forEach((function(t){var n,o,i;n=e,o=t,i=r[t],(o=x(o))in n?Object.defineProperty(n,o,{value:i,enumerable:!0,configurable:!0,writable:!0}):n[o]=i})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):I(Object(r)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))}))}return e}function w(e){return"Minified Redux error #"+e+"; visit https://redux.js.org/Errors?code="+e+" for the full message or use the non-minified dev environment for full errors. "}var S="function"==typeof Symbol&&Symbol.observable||"@@observable",E=function(){return Math.random().toString(36).substring(7).split("").join(".")},C={INIT:"@@redux/INIT"+E(),REPLACE:"@@redux/REPLACE"+E(),PROBE_UNKNOWN_ACTION:function(){return"@@redux/PROBE_UNKNOWN_ACTION"+E()}};function O(e,t,r){var n;if("function"==typeof t&&"function"==typeof r||"function"==typeof r&&"function"==typeof arguments[3])throw new Error(w(0));if("function"==typeof t&&void 0===r&&(r=t,t=void 0),void 0!==r){if("function"!=typeof r)throw new Error(w(1));return r(O)(e,t)}if("function"!=typeof e)throw new Error(w(2));var o=e,i=t,a=[],s=a,l=!1;function c(){s===a&&(s=a.slice())}function d(){if(l)throw new Error(w(3));return i}function u(e){if("function"!=typeof e)throw new Error(w(4));if(l)throw new Error(w(5));var t=!0;return c(),s.push(e),function(){if(t){if(l)throw new Error(w(6));t=!1,c();var r=s.indexOf(e);s.splice(r,1),a=null}}}function p(e){if(!function(e){if("object"!=typeof e||null===e)return!1;for(var t=e;null!==Object.getPrototypeOf(t);)t=Object.getPrototypeOf(t);return Object.getPrototypeOf(e)===t}(e))throw new Error(w(7));if(void 0===e.type)throw new Error(w(8));if(l)throw new Error(w(9));try{l=!0,i=o(i,e)}finally{l=!1}for(var t=a=s,r=0;r<t.length;r++){(0,t[r])()}return e}return p({type:C.INIT}),(n={dispatch:p,subscribe:u,getState:d,replaceReducer:function(e){if("function"!=typeof e)throw new Error(w(10));o=e,p({type:C.REPLACE})}})[S]=function(){var e,t=u;return(e={subscribe:function(e){if("object"!=typeof e||null===e)throw new Error(w(11));function r(){e.next&&e.next(d())}return r(),{unsubscribe:t(r)}}})[S]=function(){return this},e},n}function A(e,t){return function(){return t(e.apply(this,arguments))}}function P(e,t){if("function"==typeof e)return A(e,t);if("object"!=typeof e||null===e)throw new Error(w(16));var r={};for(var n in e){var o=e[n];"function"==typeof o&&(r[n]=A(o,t))}return r}function R(){for(var e=arguments.length,t=new Array(e),r=0;r<e;r++)t[r]=arguments[r];return 0===t.length?function(e){return e}:1===t.length?t[0]:t.reduce((function(e,t){return function(){return e(t.apply(void 0,arguments))}}))}let N=function(e){e()};const B=()=>N,T=Symbol.for("react-redux-context"),L="undefined"!=typeof globalThis?globalThis:{};function M(){var e;if(!i.createContext)return{};const t=null!=(e=L[T])?e:L[T]=new Map;let r=t.get(i.createContext);return r||(r=i.createContext(null),t.set(i.createContext,r)),r}const G=M();function _(){return _=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e},_.apply(this,arguments)}function $(e,t){if(null==e)return{};var r,n,o={},i=Object.keys(e);for(n=0;n<i.length;n++)r=i[n],t.indexOf(r)>=0||(o[r]=e[r]);return o}function F(e){return e&&e.__esModule&&Object.prototype.hasOwnProperty.call(e,"default")?e.default:e}var j,k={exports:{}},W={};k.exports=function(){if(j)return W;j=1;var e="function"==typeof Symbol&&Symbol.for,t=e?Symbol.for("react.element"):60103,r=e?Symbol.for("react.portal"):60106,n=e?Symbol.for("react.fragment"):60107,o=e?Symbol.for("react.strict_mode"):60108,i=e?Symbol.for("react.profiler"):60114,a=e?Symbol.for("react.provider"):60109,s=e?Symbol.for("react.context"):60110,l=e?Symbol.for("react.async_mode"):60111,c=e?Symbol.for("react.concurrent_mode"):60111,d=e?Symbol.for("react.forward_ref"):60112,u=e?Symbol.for("react.suspense"):60113,p=e?Symbol.for("react.suspense_list"):60120,f=e?Symbol.for("react.memo"):60115,g=e?Symbol.for("react.lazy"):60116,m=e?Symbol.for("react.block"):60121,b=e?Symbol.for("react.fundamental"):60117,h=e?Symbol.for("react.responder"):60118,y=e?Symbol.for("react.scope"):60119;function v(e){if("object"==typeof e&&null!==e){var p=e.$$typeof;switch(p){case t:switch(e=e.type){case l:case c:case n:case i:case o:case u:return e;default:switch(e=e&&e.$$typeof){case s:case d:case g:case f:case a:return e;default:return p}}case r:return p}}}function x(e){return v(e)===c}return W.AsyncMode=l,W.ConcurrentMode=c,W.ContextConsumer=s,W.ContextProvider=a,W.Element=t,W.ForwardRef=d,W.Fragment=n,W.Lazy=g,W.Memo=f,W.Portal=r,W.Profiler=i,W.StrictMode=o,W.Suspense=u,W.isAsyncMode=function(e){return x(e)||v(e)===l},W.isConcurrentMode=x,W.isContextConsumer=function(e){return v(e)===s},W.isContextProvider=function(e){return v(e)===a},W.isElement=function(e){return"object"==typeof e&&null!==e&&e.$$typeof===t},W.isForwardRef=function(e){return v(e)===d},W.isFragment=function(e){return v(e)===n},W.isLazy=function(e){return v(e)===g},W.isMemo=function(e){return v(e)===f},W.isPortal=function(e){return v(e)===r},W.isProfiler=function(e){return v(e)===i},W.isStrictMode=function(e){return v(e)===o},W.isSuspense=function(e){return v(e)===u},W.isValidElementType=function(e){return"string"==typeof e||"function"==typeof e||e===n||e===c||e===i||e===o||e===u||e===p||"object"==typeof e&&null!==e&&(e.$$typeof===g||e.$$typeof===f||e.$$typeof===a||e.$$typeof===s||e.$$typeof===d||e.$$typeof===b||e.$$typeof===h||e.$$typeof===y||e.$$typeof===m)},W.typeOf=v,W}();var U=k.exports,H={childContextTypes:!0,contextType:!0,contextTypes:!0,defaultProps:!0,displayName:!0,getDefaultProps:!0,getDerivedStateFromError:!0,getDerivedStateFromProps:!0,mixins:!0,propTypes:!0,type:!0},q={name:!0,length:!0,prototype:!0,caller:!0,callee:!0,arguments:!0,arity:!0},V={$$typeof:!0,compare:!0,defaultProps:!0,displayName:!0,propTypes:!0,type:!0},z={};function Y(e){return U.isMemo(e)?V:z[e.$$typeof]||H}z[U.ForwardRef]={$$typeof:!0,render:!0,defaultProps:!0,displayName:!0,propTypes:!0},z[U.Memo]=V;var J=Object.defineProperty,X=Object.getOwnPropertyNames,K=Object.getOwnPropertySymbols,Q=Object.getOwnPropertyDescriptor,Z=Object.getPrototypeOf,ee=Object.prototype;var te,re=function e(t,r,n){if("string"!=typeof r){if(ee){var o=Z(r);o&&o!==ee&&e(t,o,n)}var i=X(r);K&&(i=i.concat(K(r)));for(var a=Y(t),s=Y(r),l=0;l<i.length;++l){var c=i[l];if(!(q[c]||n&&n[c]||s&&s[c]||a&&a[c])){var d=Q(r,c);try{J(t,c,d)}catch(e){}}}}return t},ne=F(re),oe={exports:{}},ie={};oe.exports=function(){if(te)return ie;te=1;var e,t=Symbol.for("react.element"),r=Symbol.for("react.portal"),n=Symbol.for("react.fragment"),o=Symbol.for("react.strict_mode"),i=Symbol.for("react.profiler"),a=Symbol.for("react.provider"),s=Symbol.for("react.context"),l=Symbol.for("react.server_context"),c=Symbol.for("react.forward_ref"),d=Symbol.for("react.suspense"),u=Symbol.for("react.suspense_list"),p=Symbol.for("react.memo"),f=Symbol.for("react.lazy"),g=Symbol.for("react.offscreen");function m(e){if("object"==typeof e&&null!==e){var g=e.$$typeof;switch(g){case t:switch(e=e.type){case n:case i:case o:case d:case u:return e;default:switch(e=e&&e.$$typeof){case l:case s:case c:case f:case p:case a:return e;default:return g}}case r:return g}}}return e=Symbol.for("react.module.reference"),ie.ContextConsumer=s,ie.ContextProvider=a,ie.Element=t,ie.ForwardRef=c,ie.Fragment=n,ie.Lazy=f,ie.Memo=p,ie.Portal=r,ie.Profiler=i,ie.StrictMode=o,ie.Suspense=d,ie.SuspenseList=u,ie.isAsyncMode=function(){return!1},ie.isConcurrentMode=function(){return!1},ie.isContextConsumer=function(e){return m(e)===s},ie.isContextProvider=function(e){return m(e)===a},ie.isElement=function(e){return"object"==typeof e&&null!==e&&e.$$typeof===t},ie.isForwardRef=function(e){return m(e)===c},ie.isFragment=function(e){return m(e)===n},ie.isLazy=function(e){return m(e)===f},ie.isMemo=function(e){return m(e)===p},ie.isPortal=function(e){return m(e)===r},ie.isProfiler=function(e){return m(e)===i},ie.isStrictMode=function(e){return m(e)===o},ie.isSuspense=function(e){return m(e)===d},ie.isSuspenseList=function(e){return m(e)===u},ie.isValidElementType=function(t){return"string"==typeof t||"function"==typeof t||t===n||t===i||t===o||t===d||t===u||t===g||"object"==typeof t&&null!==t&&(t.$$typeof===f||t.$$typeof===p||t.$$typeof===a||t.$$typeof===s||t.$$typeof===c||t.$$typeof===e||void 0!==t.getModuleId)},ie.typeOf=m,ie}();var ae=oe.exports;const se=["initMapStateToProps","initMapDispatchToProps","initMergeProps"];function le(e,t,r,n,{areStatesEqual:o,areOwnPropsEqual:i,areStatePropsEqual:a}){let s,l,c,d,u,p=!1;function f(p,f){const g=!i(f,l),m=!o(p,s,f,l);return s=p,l=f,g&&m?(c=e(s,l),t.dependsOnOwnProps&&(d=t(n,l)),u=r(c,d,l),u):g?(e.dependsOnOwnProps&&(c=e(s,l)),t.dependsOnOwnProps&&(d=t(n,l)),u=r(c,d,l),u):m?function(){const t=e(s,l),n=!a(t,c);return c=t,n&&(u=r(c,d,l)),u}():u}return function(o,i){return p?f(o,i):(s=o,l=i,c=e(s,l),d=t(n,l),u=r(c,d,l),p=!0,u)}}function ce(e){return function(t){const r=e(t);function n(){return r}return n.dependsOnOwnProps=!1,n}}function de(e){return e.dependsOnOwnProps?Boolean(e.dependsOnOwnProps):1!==e.length}function ue(e,t){return function(t,{displayName:r}){const n=function(e,t){return n.dependsOnOwnProps?n.mapToProps(e,t):n.mapToProps(e,void 0)};return n.dependsOnOwnProps=!0,n.mapToProps=function(t,r){n.mapToProps=e,n.dependsOnOwnProps=de(e);let o=n(t,r);return"function"==typeof o&&(n.mapToProps=o,n.dependsOnOwnProps=de(o),o=n(t,r)),o},n}}function pe(e,t){return(r,n)=>{throw new Error(`Invalid value of type ${typeof e} for ${t} argument when connecting component ${n.wrappedComponentName}.`)}}function fe(e,t,r){return _({},r,e,t)}const ge={notify(){},get:()=>[]};function me(e,t){let r,n=ge,o=0,i=!1;function a(){c.onStateChange&&c.onStateChange()}function s(){o++,r||(r=t?t.addNestedSub(a):e.subscribe(a),n=function(){const e=B();let t=null,r=null;return{clear(){t=null,r=null},notify(){e((()=>{let e=t;for(;e;)e.callback(),e=e.next}))},get(){let e=[],r=t;for(;r;)e.push(r),r=r.next;return e},subscribe(e){let n=!0,o=r={callback:e,next:null,prev:r};return o.prev?o.prev.next=o:t=o,function(){n&&null!==t&&(n=!1,o.next?o.next.prev=o.prev:r=o.prev,o.prev?o.prev.next=o.next:t=o.next)}}}}())}function l(){o--,r&&0===o&&(r(),r=void 0,n.clear(),n=ge)}const c={addNestedSub:function(e){s();const t=n.subscribe(e);let r=!1;return()=>{r||(r=!0,t(),l())}},notifyNestedSubs:function(){n.notify()},handleChangeWrapper:a,isSubscribed:function(){return i},trySubscribe:function(){i||(i=!0,s())},tryUnsubscribe:function(){i&&(i=!1,l())},getListeners:()=>n};return c}const be=!("undefined"==typeof window||void 0===window.document||void 0===window.document.createElement)?i.useLayoutEffect:i.useEffect;function he(e,t){return e===t?0!==e||0!==t||1/e==1/t:e!=e&&t!=t}function ye(e,t){if(he(e,t))return!0;if("object"!=typeof e||null===e||"object"!=typeof t||null===t)return!1;const r=Object.keys(e),n=Object.keys(t);if(r.length!==n.length)return!1;for(let n=0;n<r.length;n++)if(!Object.prototype.hasOwnProperty.call(t,r[n])||!he(e[r[n]],t[r[n]]))return!1;return!0}const ve=["reactReduxForwardedRef"];let xe=()=>{throw new Error("uSES not initialized!")};const Ie=[null,null];function De(e,t,r,n,o,i){e.current=n,r.current=!1,o.current&&(o.current=null,i())}function we(e,t){return e===t}function Se(e,t,r,{pure:n,areStatesEqual:o=we,areOwnPropsEqual:a=ye,areStatePropsEqual:s=ye,areMergedPropsEqual:l=ye,forwardRef:c=!1,context:d=G}={}){const u=d,p=function(e){return e?"function"==typeof e?ue(e):pe(e,"mapStateToProps"):ce((()=>({})))}(e),f=function(e){return e&&"object"==typeof e?ce((t=>function(e,t){const r={};for(const n in e){const o=e[n];"function"==typeof o&&(r[n]=(...e)=>t(o(...e)))}return r}(e,t))):e?"function"==typeof e?ue(e):pe(e,"mapDispatchToProps"):ce((e=>({dispatch:e})))}(t),g=function(e){return e?"function"==typeof e?function(e){return function(t,{displayName:r,areMergedPropsEqual:n}){let o,i=!1;return function(t,r,a){const s=e(t,r,a);return i?n(s,o)||(o=s):(i=!0,o=s),o}}}(e):pe(e,"mergeProps"):()=>fe}(r),m=Boolean(e);return e=>{const t=e.displayName||e.name||"Component",r=`Connect(${t})`,n={shouldHandleStateChanges:m,displayName:r,wrappedComponentName:t,WrappedComponent:e,initMapStateToProps:p,initMapDispatchToProps:f,initMergeProps:g,areStatesEqual:o,areStatePropsEqual:s,areOwnPropsEqual:a,areMergedPropsEqual:l};function d(t){const[r,o,a]=i.useMemo((()=>{const{reactReduxForwardedRef:e}=t,r=$(t,ve);return[t.context,e,r]}),[t]),s=i.useMemo((()=>r&&r.Consumer&&ae.isContextConsumer(i.createElement(r.Consumer,null))?r:u),[r,u]),l=i.useContext(s),c=Boolean(t.store)&&Boolean(t.store.getState)&&Boolean(t.store.dispatch),d=Boolean(l)&&Boolean(l.store),p=c?t.store:l.store,f=d?l.getServerState:p.getState,g=i.useMemo((()=>function(e,t){let{initMapStateToProps:r,initMapDispatchToProps:n,initMergeProps:o}=t,i=$(t,se);return le(r(e,i),n(e,i),o(e,i),e,i)}(p.dispatch,n)),[p]),[b,h]=i.useMemo((()=>{if(!m)return Ie;const e=me(p,c?void 0:l.subscription),t=e.notifyNestedSubs.bind(e);return[e,t]}),[p,c,l]),y=i.useMemo((()=>c?l:_({},l,{subscription:b})),[c,l,b]),v=i.useRef(),x=i.useRef(a),I=i.useRef(),D=i.useRef(!1);i.useRef(!1);const w=i.useRef(!1),S=i.useRef();be((()=>(w.current=!0,()=>{w.current=!1})),[]);const E=i.useMemo((()=>()=>I.current&&a===x.current?I.current:g(p.getState(),a)),[p,a]),C=i.useMemo((()=>e=>b?function(e,t,r,n,o,i,a,s,l,c,d){if(!e)return()=>{};let u=!1,p=null;const f=()=>{if(u||!s.current)return;const e=t.getState();let r,f;try{r=n(e,o.current)}catch(e){f=e,p=e}f||(p=null),r===i.current?a.current||c():(i.current=r,l.current=r,a.current=!0,d())};return r.onStateChange=f,r.trySubscribe(),f(),()=>{if(u=!0,r.tryUnsubscribe(),r.onStateChange=null,p)throw p}}(m,p,b,g,x,v,D,w,I,h,e):()=>{}),[b]);var O,A,P;let R;O=De,A=[x,v,D,a,I,h],be((()=>O(...A)),P);try{R=xe(C,E,f?()=>g(f(),a):E)}catch(e){throw S.current&&(e.message+=`\nThe error may be correlated with this previous error:\n${S.current.stack}\n\n`),e}be((()=>{S.current=void 0,I.current=void 0,v.current=R}));const N=i.useMemo((()=>i.createElement(e,_({},R,{ref:o}))),[o,e,R]);return i.useMemo((()=>m?i.createElement(s.Provider,{value:y},N):N),[s,N,y])}const b=i.memo(d);if(b.WrappedComponent=e,b.displayName=d.displayName=r,c){const t=i.forwardRef((function(e,t){return i.createElement(b,_({},e,{reactReduxForwardedRef:t}))}));return t.displayName=r,t.WrappedComponent=e,ne(t,e)}return ne(b,e)}}function Ee({store:e,context:t,children:r,serverState:n,stabilityCheck:o="once",noopCheck:a="once"}){const s=i.useMemo((()=>{const t=me(e);return{store:e,subscription:t,getServerState:n?()=>n:void 0,stabilityCheck:o,noopCheck:a}}),[e,n,o,a]),l=i.useMemo((()=>e.getState()),[e]);be((()=>{const{subscription:t}=s;return t.onStateChange=t.notifyNestedSubs,t.trySubscribe(),l!==e.getState()&&t.notifyNestedSubs(),()=>{t.tryUnsubscribe(),t.onStateChange=void 0}}),[s,l]);const c=t||G;return i.createElement(c.Provider,{value:s},r)}var Ce,Oe;function Ae(e,r){var n=t.useState((function(){return{inputs:r,result:e()}}))[0],o=t.useRef(!0),i=t.useRef(n),a=o.current||Boolean(r&&i.current.inputs&&function(e,t){if(e.length!==t.length)return!1;for(var r=0;r<e.length;r++)if(e[r]!==t[r])return!1;return!0}(r,i.current.inputs))?i.current:{inputs:r,result:e()};return t.useEffect((function(){o.current=!1,i.current=a}),[a]),a.result}Ce=n.useSyncExternalStore,xe=Ce,Oe=r.unstable_batchedUpdates,N=Oe;var Pe=Ae,Re=function(e,t){return Ae((function(){return e}),t)};const Ne={x:0,y:0},Be=(e,t)=>({x:e.x+t.x,y:e.y+t.y}),Te=(e,t)=>({x:e.x-t.x,y:e.y-t.y}),Le=(e,t)=>e.x===t.x&&e.y===t.y,Me=e=>({x:0!==e.x?-e.x:0,y:0!==e.y?-e.y:0}),Ge=(e,t,r=0)=>"x"===e?{x:t,y:r}:{x:r,y:t},_e=(e,t)=>Math.sqrt((t.x-e.x)**2+(t.y-e.y)**2),$e=(e,t)=>Math.min(...t.map((t=>_e(e,t)))),Fe=e=>t=>({x:e(t.x),y:e(t.y)});var je=function(e){var t=e.top,r=e.right,n=e.bottom,o=e.left;return{top:t,right:r,bottom:n,left:o,width:r-o,height:n-t,x:o,y:t,center:{x:(r+o)/2,y:(n+t)/2}}},ke=function(e,t){return{top:e.top-t.top,left:e.left-t.left,bottom:e.bottom+t.bottom,right:e.right+t.right}},We=function(e,t){return{top:e.top+t.top,left:e.left+t.left,bottom:e.bottom-t.bottom,right:e.right-t.right}},Ue={top:0,right:0,bottom:0,left:0},He=function(e){var t=e.borderBox,r=e.margin,n=void 0===r?Ue:r,o=e.border,i=void 0===o?Ue:o,a=e.padding,s=void 0===a?Ue:a,l=je(ke(t,n)),c=je(We(t,i)),d=je(We(c,s));return{marginBox:l,borderBox:je(t),paddingBox:c,contentBox:d,margin:n,border:i,padding:s}},qe=function(e){var t=e.slice(0,-2);if("px"!==e.slice(-2))return 0;var r=Number(t);return isNaN(r)&&function(e,t){if(!e)throw new Error("Invariant failed")}(!1),r},Ve=function(e,t){var r,n,o=e.borderBox,i=e.border,a=e.margin,s=e.padding,l=(n=t,{top:(r=o).top+n.y,left:r.left+n.x,bottom:r.bottom+n.y,right:r.right+n.x});return He({borderBox:l,border:i,margin:a,padding:s})},ze=function(e,t){return void 0===t&&(t={x:window.pageXOffset,y:window.pageYOffset}),Ve(e,t)},Ye=function(e,t){var r={top:qe(t.marginTop),right:qe(t.marginRight),bottom:qe(t.marginBottom),left:qe(t.marginLeft)},n={top:qe(t.paddingTop),right:qe(t.paddingRight),bottom:qe(t.paddingBottom),left:qe(t.paddingLeft)},o={top:qe(t.borderTopWidth),right:qe(t.borderRightWidth),bottom:qe(t.borderBottomWidth),left:qe(t.borderLeftWidth)};return He({borderBox:e,margin:r,padding:n,border:o})},Je=function(e){var t=e.getBoundingClientRect(),r=window.getComputedStyle(e);return Ye(t,r)};const Xe=(e,t)=>({top:e.top+t.y,left:e.left+t.x,bottom:e.bottom+t.y,right:e.right+t.x}),Ke=e=>[{x:e.left,y:e.top},{x:e.right,y:e.top},{x:e.left,y:e.bottom},{x:e.right,y:e.bottom}],Qe=(e,t)=>t&&t.shouldClipSubject?((e,t)=>{const r=je({top:Math.max(t.top,e.top),right:Math.min(t.right,e.right),bottom:Math.min(t.bottom,e.bottom),left:Math.max(t.left,e.left)});return r.width<=0||r.height<=0?null:r})(t.pageMarginBox,e):je(e);var Ze=({page:e,withPlaceholder:t,axis:r,frame:n})=>{const o=((e,t)=>t?Xe(e,t.scroll.diff.displacement):e)(e.marginBox,n),i=((e,t,r)=>r&&r.increasedBy?{...e,[t.end]:e[t.end]+r.increasedBy[t.line]}:e)(o,r,t);return{page:e,withPlaceholder:t,active:Qe(i,n)}},et=(e,t)=>{e.frame||u(!1);const r=e.frame,n=Te(t,r.scroll.initial),o=Me(n),i={...r,scroll:{initial:r.scroll.initial,current:t,diff:{value:n,displacement:o},max:r.scroll.max}},a=Ze({page:e.subject.page,withPlaceholder:e.subject.withPlaceholder,axis:e.axis,frame:i});return{...e,frame:i,subject:a}},tt=Number.isNaN||function(e){return"number"==typeof e&&e!=e};function rt(e,t){if(e.length!==t.length)return!1;for(var r=0;r<e.length;r++)if(n=e[r],o=t[r],!(n===o||tt(n)&&tt(o)))return!1;var n,o;return!0}function nt(e,t){void 0===t&&(t=rt);var r=null;function n(){for(var n=[],o=0;o<arguments.length;o++)n[o]=arguments[o];if(r&&r.lastThis===this&&t(n,r.lastArgs))return r.lastResult;var i=e.apply(this,n);return r={lastResult:i,lastArgs:n,lastThis:this},i}return n.clear=function(){r=null},n}const ot=nt((e=>e.reduce(((e,t)=>(e[t.descriptor.id]=t,e)),{}))),it=nt((e=>e.reduce(((e,t)=>(e[t.descriptor.id]=t,e)),{}))),at=nt((e=>Object.values(e))),st=nt((e=>Object.values(e)));var lt=nt(((e,t)=>{const r=st(t).filter((t=>e===t.descriptor.droppableId)).sort(((e,t)=>e.descriptor.index-t.descriptor.index));return r}));function ct(e){return e.at&&"REORDER"===e.at.type?e.at.destination:null}function dt(e){return e.at&&"COMBINE"===e.at.type?e.at.combine:null}var ut=nt(((e,t)=>t.filter((t=>t.descriptor.id!==e.descriptor.id)))),pt=(e,t)=>e.descriptor.droppableId===t.descriptor.id;const ft={point:Ne,value:0},gt={invisible:{},visible:{},all:[]};var mt={displaced:gt,displacedBy:ft,at:null},bt=(e,t)=>r=>e<=r&&r<=t,ht=e=>{const t=bt(e.top,e.bottom),r=bt(e.left,e.right);return n=>{if(t(n.top)&&t(n.bottom)&&r(n.left)&&r(n.right))return!0;const o=t(n.top)||t(n.bottom),i=r(n.left)||r(n.right);if(o&&i)return!0;const a=n.top<e.top&&n.bottom>e.bottom,s=n.left<e.left&&n.right>e.right;if(a&&s)return!0;return a&&i||s&&o}},yt=e=>{const t=bt(e.top,e.bottom),r=bt(e.left,e.right);return e=>t(e.top)&&t(e.bottom)&&r(e.left)&&r(e.right)};const vt={direction:"vertical",line:"y",crossAxisLine:"x",start:"top",end:"bottom",size:"height",crossAxisStart:"left",crossAxisEnd:"right",crossAxisSize:"width"},xt={direction:"horizontal",line:"x",crossAxisLine:"y",start:"left",end:"right",size:"width",crossAxisStart:"top",crossAxisEnd:"bottom",crossAxisSize:"height"};const It=({target:e,destination:t,viewport:r,withDroppableDisplacement:n,isVisibleThroughFrameFn:o})=>{const i=n?((e,t)=>{const r=t.frame?t.frame.scroll.diff.displacement:Ne;return Xe(e,r)})(e,t):e;return((e,t,r)=>!!t.subject.active&&r(t.subject.active)(e))(i,t,o)&&((e,t,r)=>r(t)(e))(i,r,o)},Dt=e=>It({...e,isVisibleThroughFrameFn:ht}),wt=e=>It({...e,isVisibleThroughFrameFn:yt}),St=(e,t,r)=>{if("boolean"==typeof r)return r;if(!t)return!0;const{invisible:n,visible:o}=t;if(n[e])return!1;const i=o[e];return!i||i.shouldAnimate};function Et({afterDragging:e,destination:t,displacedBy:r,viewport:n,forceShouldAnimate:o,last:i}){return e.reduce((function(e,a){const s=function(e,t){const r=e.page.marginBox,n={top:t.point.y,right:0,bottom:0,left:t.point.x};return je(ke(r,n))}(a,r),l=a.descriptor.id;e.all.push(l);if(!Dt({target:s,destination:t,viewport:n,withDroppableDisplacement:!0}))return e.invisible[a.descriptor.id]=!0,e;const c={draggableId:l,shouldAnimate:St(l,i,o)};return e.visible[l]=c,e}),{all:[],visible:{},invisible:{}})}function Ct({insideDestination:e,inHomeList:t,displacedBy:r,destination:n}){const o=function(e,t){if(!e.length)return 0;const r=e[e.length-1].descriptor.index;return t.inHomeList?r:r+1}(e,{inHomeList:t});return{displaced:gt,displacedBy:r,at:{type:"REORDER",destination:{droppableId:n.descriptor.id,index:o}}}}function Ot({draggable:e,insideDestination:t,destination:r,viewport:n,displacedBy:o,last:i,index:a,forceShouldAnimate:s}){const l=pt(e,r);if(null==a)return Ct({insideDestination:t,inHomeList:l,displacedBy:o,destination:r});const c=t.find((e=>e.descriptor.index===a));if(!c)return Ct({insideDestination:t,inHomeList:l,displacedBy:o,destination:r});const d=ut(e,t),u=t.indexOf(c);return{displaced:Et({afterDragging:d.slice(u),destination:r,displacedBy:o,last:i,viewport:n.frame,forceShouldAnimate:s}),displacedBy:o,at:{type:"REORDER",destination:{droppableId:r.descriptor.id,index:a}}}}function At(e,t){return Boolean(t.effected[e])}var Pt=({isMovingForward:e,isInHomeList:t,draggable:r,draggables:n,destination:o,insideDestination:i,previousImpact:a,viewport:s,afterCritical:l})=>{const c=a.at;if(c||u(!1),"REORDER"===c.type){const n=(({isMovingForward:e,isInHomeList:t,insideDestination:r,location:n})=>{if(!r.length)return null;const o=n.index,i=e?o+1:o-1,a=r[0].descriptor.index,s=r[r.length-1].descriptor.index;return i<a||i>(t?s:s+1)?null:i})({isMovingForward:e,isInHomeList:t,location:c.destination,insideDestination:i});return null==n?null:Ot({draggable:r,insideDestination:i,destination:o,viewport:s,last:a.displaced,displacedBy:a.displacedBy,index:n})}const d=(({isMovingForward:e,destination:t,draggables:r,combine:n,afterCritical:o})=>{if(!t.isCombineEnabled)return null;const i=n.draggableId,a=r[i].descriptor.index;return At(i,o)?e?a:a-1:e?a+1:a})({isMovingForward:e,destination:o,displaced:a.displaced,draggables:n,combine:c.combine,afterCritical:l});return null==d?null:Ot({draggable:r,insideDestination:i,destination:o,viewport:s,last:a.displaced,displacedBy:a.displacedBy,index:d})},Rt=({afterCritical:e,impact:t,draggables:r})=>{const n=dt(t);n||u(!1);const o=n.draggableId,i=r[o].page.borderBox.center,a=(({displaced:e,afterCritical:t,combineWith:r,displacedBy:n})=>{const o=Boolean(e.visible[r]||e.invisible[r]);return At(r,t)?o?Ne:Me(n.point):o?n.point:Ne})({displaced:t.displaced,afterCritical:e,combineWith:o,displacedBy:t.displacedBy});return Be(i,a)};const Nt=(e,t)=>t.margin[e.start]+t.borderBox[e.size]/2,Bt=(e,t,r)=>t[e.crossAxisStart]+r.margin[e.crossAxisStart]+r.borderBox[e.crossAxisSize]/2,Tt=({axis:e,moveRelativeTo:t,isMoving:r})=>Ge(e.line,t.marginBox[e.end]+Nt(e,r),Bt(e,t.marginBox,r)),Lt=({axis:e,moveRelativeTo:t,isMoving:r})=>Ge(e.line,t.marginBox[e.start]-((e,t)=>t.margin[e.end]+t.borderBox[e.size]/2)(e,r),Bt(e,t.marginBox,r));var Mt=({impact:e,draggable:t,draggables:r,droppable:n,afterCritical:o})=>{const i=lt(n.descriptor.id,r),a=t.page,s=n.axis;if(!i.length)return(({axis:e,moveInto:t,isMoving:r})=>Ge(e.line,t.contentBox[e.start]+Nt(e,r),Bt(e,t.contentBox,r)))({axis:s,moveInto:n.page,isMoving:a});const{displaced:l,displacedBy:c}=e,d=l.all[0];if(d){const e=r[d];if(At(d,o))return Lt({axis:s,moveRelativeTo:e.page,isMoving:a});const t=Ve(e.page,c.point);return Lt({axis:s,moveRelativeTo:t,isMoving:a})}const u=i[i.length-1];if(u.descriptor.id===t.descriptor.id)return a.borderBox.center;if(At(u.descriptor.id,o)){const e=Ve(u.page,Me(o.displacedBy.point));return Tt({axis:s,moveRelativeTo:e,isMoving:a})}return Tt({axis:s,moveRelativeTo:u.page,isMoving:a})},Gt=(e,t)=>{const r=e.frame;return r?Be(t,r.scroll.diff.displacement):t};var _t=e=>{const t=(({impact:e,draggable:t,droppable:r,draggables:n,afterCritical:o})=>{const i=t.page.borderBox.center,a=e.at;return r&&a?"REORDER"===a.type?Mt({impact:e,draggable:t,draggables:n,droppable:r,afterCritical:o}):Rt({impact:e,draggables:n,afterCritical:o}):i})(e),r=e.droppable;return r?Gt(r,t):t},$t=(e,t)=>{const r=Te(t,e.scroll.initial),n=Me(r);return{frame:je({top:t.y,bottom:t.y+e.frame.height,left:t.x,right:t.x+e.frame.width}),scroll:{initial:e.scroll.initial,max:e.scroll.max,current:t,diff:{value:r,displacement:n}}}};function Ft(e,t){return e.map((e=>t[e]))}var jt=({pageBorderBoxCenter:e,draggable:t,viewport:r})=>{const n=((e,t)=>Be(e.scroll.diff.displacement,t))(r,e),o=Te(n,t.page.borderBox.center);return Be(t.client.borderBox.center,o)},kt=({draggable:e,destination:t,newPageBorderBoxCenter:r,viewport:n,withDroppableDisplacement:o,onlyOnMainAxis:i=!1})=>{const a=Te(r,e.page.borderBox.center),s={target:Xe(e.page.borderBox,a),destination:t,withDroppableDisplacement:o,viewport:n};return i?(e=>{return It({...e,isVisibleThroughFrameFn:(t=e.destination.axis,e=>{const r=bt(e.top,e.bottom),n=bt(e.left,e.right);return e=>t===vt?r(e.top)&&r(e.bottom):n(e.left)&&n(e.right)})});var t})(s):wt(s)},Wt=({isMovingForward:e,draggable:t,destination:r,draggables:n,previousImpact:o,viewport:i,previousPageBorderBoxCenter:a,previousClientSelection:s,afterCritical:l})=>{if(!r.isEnabled)return null;const c=lt(r.descriptor.id,n),d=pt(t,r),p=(({isMovingForward:e,draggable:t,destination:r,insideDestination:n,previousImpact:o})=>{if(!r.isCombineEnabled)return null;if(!ct(o))return null;function i(e){const t={type:"COMBINE",combine:{draggableId:e,droppableId:r.descriptor.id}};return{...o,at:t}}const a=o.displaced.all,s=a.length?a[0]:null;if(e)return s?i(s):null;const l=ut(t,n);if(!s)return l.length?i(l[l.length-1].descriptor.id):null;const c=l.findIndex((e=>e.descriptor.id===s));-1===c&&u(!1);const d=c-1;return d<0?null:i(l[d].descriptor.id)})({isMovingForward:e,draggable:t,destination:r,insideDestination:c,previousImpact:o})||Pt({isMovingForward:e,isInHomeList:d,draggable:t,draggables:n,destination:r,insideDestination:c,previousImpact:o,viewport:i,afterCritical:l});if(!p)return null;const f=_t({impact:p,draggable:t,droppable:r,draggables:n,afterCritical:l});if(kt({draggable:t,destination:r,newPageBorderBoxCenter:f,viewport:i.frame,withDroppableDisplacement:!1,onlyOnMainAxis:!0})){return{clientSelection:jt({pageBorderBoxCenter:f,draggable:t,viewport:i}),impact:p,scrollJumpRequest:null}}const g=Te(f,a),m=(({impact:e,viewport:t,destination:r,draggables:n,maxScrollChange:o})=>{const i=$t(t,Be(t.scroll.current,o)),a=r.frame?et(r,Be(r.frame.scroll.current,o)):r,s=e.displaced,l=Et({afterDragging:Ft(s.all,n),destination:r,displacedBy:e.displacedBy,viewport:i.frame,last:s,forceShouldAnimate:!1}),c=Et({afterDragging:Ft(s.all,n),destination:a,displacedBy:e.displacedBy,viewport:t.frame,last:s,forceShouldAnimate:!1}),d={},u={},p=[s,l,c];return s.all.forEach((e=>{const t=function(e,t){for(let r=0;r<t.length;r++){const n=t[r].visible[e];if(n)return n}return null}(e,p);t?u[e]=t:d[e]=!0})),{...e,displaced:{all:s.all,invisible:d,visible:u}}})({impact:p,viewport:i,destination:r,draggables:n,maxScrollChange:g});return{clientSelection:s,impact:m,scrollJumpRequest:g}};const Ut=e=>{const t=e.subject.active;return t||u(!1),t};const Ht=(e,t)=>{const r=e.page.borderBox.center;return At(e.descriptor.id,t)?Te(r,t.displacedBy.point):r},qt=(e,t)=>{const r=e.page.borderBox;return At(e.descriptor.id,t)?Xe(r,Me(t.displacedBy.point)):r};var Vt=nt((function(e,t){const r=t[e.line];return{value:r,point:Ge(e.line,r)}}));const zt=(e,t)=>({...e,scroll:{...e.scroll,max:t}}),Yt=(e,t,r)=>{const n=e.frame;pt(t,e)&&u(!1),e.subject.withPlaceholder&&u(!1);const o=Vt(e.axis,t.displaceBy).point,i=((e,t,r)=>{const n=e.axis;if("virtual"===e.descriptor.mode)return Ge(n.line,t[n.line]);const o=e.subject.page.contentBox[n.size],i=lt(e.descriptor.id,r).reduce(((e,t)=>e+t.client.marginBox[n.size]),0)+t[n.line]-o;return i<=0?null:Ge(n.line,i)})(e,o,r),a={placeholderSize:o,increasedBy:i,oldFrameMaxScroll:e.frame?e.frame.scroll.max:null};if(!n){const t=Ze({page:e.subject.page,withPlaceholder:a,axis:e.axis,frame:e.frame});return{...e,subject:t}}const s=i?Be(n.scroll.max,i):n.scroll.max,l=zt(n,s),c=Ze({page:e.subject.page,withPlaceholder:a,axis:e.axis,frame:l});return{...e,subject:c,frame:l}};var Jt=({isMovingForward:e,previousPageBorderBoxCenter:t,draggable:r,isOver:n,draggables:o,droppables:i,viewport:a,afterCritical:s})=>{const l=(({isMovingForward:e,pageBorderBoxCenter:t,source:r,droppables:n,viewport:o})=>{const i=r.subject.active;if(!i)return null;const a=r.axis,s=bt(i[a.start],i[a.end]),l=at(n).filter((e=>e!==r)).filter((e=>e.isEnabled)).filter((e=>Boolean(e.subject.active))).filter((e=>ht(o.frame)(Ut(e)))).filter((t=>{const r=Ut(t);return e?i[a.crossAxisEnd]<r[a.crossAxisEnd]:r[a.crossAxisStart]<i[a.crossAxisStart]})).filter((e=>{const t=Ut(e),r=bt(t[a.start],t[a.end]);return s(t[a.start])||s(t[a.end])||r(i[a.start])||r(i[a.end])})).sort(((t,r)=>{const n=Ut(t)[a.crossAxisStart],o=Ut(r)[a.crossAxisStart];return e?n-o:o-n})).filter(((e,t,r)=>Ut(e)[a.crossAxisStart]===Ut(r[0])[a.crossAxisStart]));if(!l.length)return null;if(1===l.length)return l[0];const c=l.filter((e=>bt(Ut(e)[a.start],Ut(e)[a.end])(t[a.line])));return 1===c.length?c[0]:c.length>1?c.sort(((e,t)=>Ut(e)[a.start]-Ut(t)[a.start]))[0]:l.sort(((e,r)=>{const n=$e(t,Ke(Ut(e))),o=$e(t,Ke(Ut(r)));return n!==o?n-o:Ut(e)[a.start]-Ut(r)[a.start]}))[0]})({isMovingForward:e,pageBorderBoxCenter:t,source:n,droppables:i,viewport:a});if(!l)return null;const c=lt(l.descriptor.id,o),d=(({pageBorderBoxCenter:e,viewport:t,destination:r,insideDestination:n,afterCritical:o})=>{const i=n.filter((e=>wt({target:qt(e,o),destination:r,viewport:t.frame,withDroppableDisplacement:!0}))).sort(((t,n)=>{const i=_e(e,Gt(r,Ht(t,o))),a=_e(e,Gt(r,Ht(n,o)));return i<a?-1:a<i?1:t.descriptor.index-n.descriptor.index}));return i[0]||null})({pageBorderBoxCenter:t,viewport:a,destination:l,insideDestination:c,afterCritical:s}),u=(({previousPageBorderBoxCenter:e,moveRelativeTo:t,insideDestination:r,draggable:n,draggables:o,destination:i,viewport:a,afterCritical:s})=>{if(!t){if(r.length)return null;const e={displaced:gt,displacedBy:ft,at:{type:"REORDER",destination:{droppableId:i.descriptor.id,index:0}}},t=_t({impact:e,draggable:n,droppable:i,draggables:o,afterCritical:s}),l=pt(n,i)?i:Yt(i,n,o);return kt({draggable:n,destination:l,newPageBorderBoxCenter:t,viewport:a.frame,withDroppableDisplacement:!1,onlyOnMainAxis:!0})?e:null}const l=Boolean(e[i.axis.line]<=t.page.borderBox.center[i.axis.line]),c=(()=>{const e=t.descriptor.index;return t.descriptor.id===n.descriptor.id||l?e:e+1})(),d=Vt(i.axis,n.displaceBy);return Ot({draggable:n,insideDestination:r,destination:i,viewport:a,displacedBy:d,last:gt,index:c})})({previousPageBorderBoxCenter:t,destination:l,draggable:r,draggables:o,moveRelativeTo:d,insideDestination:c,viewport:a,afterCritical:s});if(!u)return null;const p=_t({impact:u,draggable:r,droppable:l,draggables:o,afterCritical:s});return{clientSelection:jt({pageBorderBoxCenter:p,draggable:r,viewport:a}),impact:u,scrollJumpRequest:null}},Xt=e=>{const t=e.at;return t?"REORDER"===t.type?t.destination.droppableId:t.combine.droppableId:null};var Kt=({state:e,type:t})=>{const r=((e,t)=>{const r=Xt(e);return r?t[r]:null})(e.impact,e.dimensions.droppables),n=Boolean(r),o=e.dimensions.droppables[e.critical.droppable.id],i=r||o,a=i.axis.direction,s="vertical"===a&&("MOVE_UP"===t||"MOVE_DOWN"===t)||"horizontal"===a&&("MOVE_LEFT"===t||"MOVE_RIGHT"===t);if(s&&!n)return null;const l="MOVE_DOWN"===t||"MOVE_RIGHT"===t,c=e.dimensions.draggables[e.critical.draggable.id],d=e.current.page.borderBoxCenter,{draggables:u,droppables:p}=e.dimensions;return s?Wt({isMovingForward:l,previousPageBorderBoxCenter:d,draggable:c,destination:i,draggables:u,viewport:e.viewport,previousClientSelection:e.current.client.selection,previousImpact:e.impact,afterCritical:e.afterCritical}):Jt({isMovingForward:l,previousPageBorderBoxCenter:d,draggable:c,isOver:i,draggables:u,droppables:p,viewport:e.viewport,afterCritical:e.afterCritical})};function Qt(e){return"DRAGGING"===e.phase||"COLLECTING"===e.phase}function Zt(e){const t=bt(e.top,e.bottom),r=bt(e.left,e.right);return function(e){return t(e.y)&&r(e.x)}}function er({pageBorderBox:e,draggable:t,droppables:r}){const n=at(r).filter((t=>{if(!t.isEnabled)return!1;const r=t.subject.active;if(!r)return!1;if(o=r,!((n=e).left<o.right&&n.right>o.left&&n.top<o.bottom&&n.bottom>o.top))return!1;var n,o;if(Zt(r)(e.center))return!0;const i=t.axis,a=r.center[i.crossAxisLine],s=e[i.crossAxisStart],l=e[i.crossAxisEnd],c=bt(r[i.crossAxisStart],r[i.crossAxisEnd]),d=c(s),u=c(l);return!d&&!u||(d?s<a:l>a)}));return n.length?1===n.length?n[0].descriptor.id:function({pageBorderBox:e,draggable:t,candidates:r}){const n=t.page.borderBox.center,o=r.map((t=>{const r=t.axis,o=Ge(t.axis.line,e.center[r.line],t.page.borderBox.center[r.crossAxisLine]);return{id:t.descriptor.id,distance:_e(n,o)}})).sort(((e,t)=>t.distance-e.distance));return o[0]?o[0].id:null}({pageBorderBox:e,draggable:t,candidates:n}):null}const tr=(e,t)=>je(Xe(e,t));function rr({displaced:e,id:t}){return Boolean(e.visible[t]||e.invisible[t])}var nr=({pageOffset:e,draggable:t,draggables:r,droppables:n,previousImpact:o,viewport:i,afterCritical:a})=>{const s=tr(t.page.borderBox,e),l=er({pageBorderBox:s,draggable:t,droppables:n});if(!l)return mt;const c=n[l],d=lt(c.descriptor.id,r),u=((e,t)=>{const r=e.frame;return r?tr(t,r.scroll.diff.value):t})(c,s);return(({draggable:e,pageBorderBoxWithDroppableScroll:t,previousImpact:r,destination:n,insideDestination:o,afterCritical:i})=>{if(!n.isCombineEnabled)return null;const a=n.axis,s=Vt(n.axis,e.displaceBy),l=s.value,c=t[a.start],d=t[a.end],u=ut(e,o).find((e=>{const t=e.descriptor.id,n=e.page.borderBox,o=n[a.size]/4,s=At(t,i),u=rr({displaced:r.displaced,id:t});return s?u?d>n[a.start]+o&&d<n[a.end]-o:c>n[a.start]-l+o&&c<n[a.end]-l-o:u?d>n[a.start]+l+o&&d<n[a.end]+l-o:c>n[a.start]+o&&c<n[a.end]-o}));return u?{displacedBy:s,displaced:r.displaced,at:{type:"COMBINE",combine:{draggableId:u.descriptor.id,droppableId:n.descriptor.id}}}:null})({pageBorderBoxWithDroppableScroll:u,draggable:t,previousImpact:o,destination:c,insideDestination:d,afterCritical:a})||(({pageBorderBoxWithDroppableScroll:e,draggable:t,destination:r,insideDestination:n,last:o,viewport:i,afterCritical:a})=>{const s=r.axis,l=Vt(r.axis,t.displaceBy),c=l.value,d=e[s.start],u=e[s.end],p=function({draggable:e,closest:t,inHomeList:r}){return t?r&&t.descriptor.index>e.descriptor.index?t.descriptor.index-1:t.descriptor.index:null}({draggable:t,closest:ut(t,n).find((e=>{const t=e.descriptor.id,r=e.page.borderBox.center[s.line],n=At(t,a),i=rr({displaced:o,id:t});return n?i?u<=r:d<r-c:i?u<=r+c:d<r}))||null,inHomeList:pt(t,r)});return Ot({draggable:t,insideDestination:n,destination:r,viewport:i,last:o,displacedBy:l,index:p})})({pageBorderBoxWithDroppableScroll:u,draggable:t,destination:c,insideDestination:d,last:o.displaced,viewport:i,afterCritical:a})},or=(e,t)=>({...e,[t.descriptor.id]:t});const ir=({previousImpact:e,impact:t,droppables:r})=>{const n=Xt(e),o=Xt(t);if(!n)return r;if(n===o)return r;const i=r[n];if(!i.subject.withPlaceholder)return r;const a=(e=>{const t=e.subject.withPlaceholder;t||u(!1);const r=e.frame;if(!r){const t=Ze({page:e.subject.page,axis:e.axis,frame:null,withPlaceholder:null});return{...e,subject:t}}const n=t.oldFrameMaxScroll;n||u(!1);const o=zt(r,n),i=Ze({page:e.subject.page,axis:e.axis,frame:o,withPlaceholder:null});return{...e,subject:i,frame:o}})(i);return or(r,a)};var ar=({state:e,clientSelection:t,dimensions:r,viewport:n,impact:o,scrollJumpRequest:i})=>{const a=n||e.viewport,s=r||e.dimensions,l=t||e.current.client.selection,c=Te(l,e.initial.client.selection),d={offset:c,selection:l,borderBoxCenter:Be(e.initial.client.borderBoxCenter,c)},u={selection:Be(d.selection,a.scroll.current),borderBoxCenter:Be(d.borderBoxCenter,a.scroll.current),offset:Be(d.offset,a.scroll.diff.value)},p={client:d,page:u};if("COLLECTING"===e.phase)return{...e,dimensions:s,viewport:a,current:p};const f=s.draggables[e.critical.draggable.id],g=o||nr({pageOffset:u.offset,draggable:f,draggables:s.draggables,droppables:s.droppables,previousImpact:e.impact,viewport:a,afterCritical:e.afterCritical}),m=(({draggable:e,draggables:t,droppables:r,previousImpact:n,impact:o})=>{const i=ir({previousImpact:n,impact:o,droppables:r}),a=Xt(o);if(!a)return i;const s=r[a];if(pt(e,s))return i;if(s.subject.withPlaceholder)return i;const l=Yt(s,e,t);return or(i,l)})({draggable:f,impact:g,previousImpact:e.impact,draggables:s.draggables,droppables:s.droppables});return{...e,current:p,dimensions:{draggables:s.draggables,droppables:m},impact:g,viewport:a,scrollJumpRequest:i||null,forceShouldAnimate:!i&&null}};var sr=({impact:e,viewport:t,draggables:r,destination:n,forceShouldAnimate:o})=>{const i=e.displaced,a=function(e,t){return e.map((e=>t[e]))}(i.all,r),s=Et({afterDragging:a,destination:n,displacedBy:e.displacedBy,viewport:t.frame,forceShouldAnimate:o,last:i});return{...e,displaced:s}},lr=({impact:e,draggable:t,droppable:r,draggables:n,viewport:o,afterCritical:i})=>{const a=_t({impact:e,draggable:t,draggables:n,droppable:r,afterCritical:i});return jt({pageBorderBoxCenter:a,draggable:t,viewport:o})},cr=({state:e,dimensions:t,viewport:r})=>{"SNAP"!==e.movementMode&&u(!1);const n=e.impact,o=r||e.viewport,i=t||e.dimensions,{draggables:a,droppables:s}=i,l=a[e.critical.draggable.id],c=Xt(n);c||u(!1);const d=s[c],p=sr({impact:n,viewport:o,destination:d,draggables:a}),f=lr({impact:p,draggable:l,droppable:d,draggables:a,viewport:o,afterCritical:e.afterCritical});return ar({impact:p,clientSelection:f,state:e,dimensions:i,viewport:o})},dr=({draggable:e,home:t,draggables:r,viewport:n})=>{const o=Vt(t.axis,e.displaceBy),i=lt(t.descriptor.id,r),a=i.indexOf(e);-1===a&&u(!1);const s=i.slice(a+1),l=s.reduce(((e,t)=>(e[t.descriptor.id]=!0,e)),{}),c={inVirtualList:"virtual"===t.descriptor.mode,displacedBy:o,effected:l};var d;return{impact:{displaced:Et({afterDragging:s,destination:t,displacedBy:o,last:null,viewport:n.frame,forceShouldAnimate:!1}),displacedBy:o,at:{type:"REORDER",destination:(d=e.descriptor,{index:d.index,droppableId:d.droppableId})}},afterCritical:c}};var ur=({additions:e,updatedDroppables:t,viewport:r})=>{const n=r.scroll.diff.value;return e.map((e=>{const o=e.descriptor.droppableId,i=(e=>{const t=e.frame;return t||u(!1),t})(t[o]),a=i.scroll.diff.value,s=(({draggable:e,offset:t,initialWindowScroll:r})=>{const n=Ve(e.client,t),o=ze(n,r);return{...e,placeholder:{...e.placeholder,client:n},client:n,page:o}})({draggable:e,offset:Be(n,a),initialWindowScroll:r.scroll.initial});return s}))};const pr=e=>"SNAP"===e.movementMode,fr=(e,t,r)=>{const n=((e,t)=>({draggables:e.draggables,droppables:or(e.droppables,t)}))(e.dimensions,t);return!pr(e)||r?ar({state:e,dimensions:n}):cr({state:e,dimensions:n})};function gr(e){return e.isDragging&&"SNAP"===e.movementMode?{...e,scrollJumpRequest:null}:e}const mr={phase:"IDLE",completed:null,shouldFlush:!1};var br=(e=mr,t)=>{if("FLUSH"===t.type)return{...mr,shouldFlush:!0};if("INITIAL_PUBLISH"===t.type){"IDLE"!==e.phase&&u(!1);const{critical:r,clientSelection:n,viewport:o,dimensions:i,movementMode:a}=t.payload,s=i.draggables[r.draggable.id],l=i.droppables[r.droppable.id],c={selection:n,borderBoxCenter:s.client.borderBox.center,offset:Ne},d={client:c,page:{selection:Be(c.selection,o.scroll.initial),borderBoxCenter:Be(c.selection,o.scroll.initial),offset:Be(c.selection,o.scroll.diff.value)}},p=at(i.droppables).every((e=>!e.isFixedOnPage)),{impact:f,afterCritical:g}=dr({draggable:s,home:l,draggables:i.draggables,viewport:o});return{phase:"DRAGGING",isDragging:!0,critical:r,movementMode:a,dimensions:i,initial:d,current:d,isWindowScrollAllowed:p,impact:f,afterCritical:g,onLiftImpact:f,viewport:o,scrollJumpRequest:null,forceShouldAnimate:null}}if("COLLECTION_STARTING"===t.type){if("COLLECTING"===e.phase||"DROP_PENDING"===e.phase)return e;"DRAGGING"!==e.phase&&u(!1);return{...e,phase:"COLLECTING"}}if("PUBLISH_WHILE_DRAGGING"===t.type)return"COLLECTING"!==e.phase&&"DROP_PENDING"!==e.phase&&u(!1),(({state:e,published:t})=>{const r=t.modified.map((t=>{const r=e.dimensions.droppables[t.droppableId];return et(r,t.scroll)})),n={...e.dimensions.droppables,...ot(r)},o=it(ur({additions:t.additions,updatedDroppables:n,viewport:e.viewport})),i={...e.dimensions.draggables,...o};t.removals.forEach((e=>{delete i[e]}));const a={droppables:n,draggables:i},s=Xt(e.impact),l=s?a.droppables[s]:null,c=a.draggables[e.critical.draggable.id],d=a.droppables[e.critical.droppable.id],{impact:u,afterCritical:p}=dr({draggable:c,home:d,draggables:i,viewport:e.viewport}),f=l&&l.isCombineEnabled?e.impact:u,g=nr({pageOffset:e.current.page.offset,draggable:a.draggables[e.critical.draggable.id],draggables:a.draggables,droppables:a.droppables,previousImpact:f,viewport:e.viewport,afterCritical:p}),m={...e,phase:"DRAGGING",impact:g,onLiftImpact:u,dimensions:a,afterCritical:p,forceShouldAnimate:!1};return"COLLECTING"===e.phase?m:{...m,phase:"DROP_PENDING",reason:e.reason,isWaiting:!1}})({state:e,published:t.payload});if("MOVE"===t.type){if("DROP_PENDING"===e.phase)return e;Qt(e)||u(!1);const{client:r}=t.payload;return Le(r,e.current.client.selection)?e:ar({state:e,clientSelection:r,impact:pr(e)?e.impact:null})}if("UPDATE_DROPPABLE_SCROLL"===t.type){if("DROP_PENDING"===e.phase)return gr(e);if("COLLECTING"===e.phase)return gr(e);Qt(e)||u(!1);const{id:r,newScroll:n}=t.payload,o=e.dimensions.droppables[r];if(!o)return e;const i=et(o,n);return fr(e,i,!1)}if("UPDATE_DROPPABLE_IS_ENABLED"===t.type){if("DROP_PENDING"===e.phase)return e;Qt(e)||u(!1);const{id:r,isEnabled:n}=t.payload,o=e.dimensions.droppables[r];o||u(!1),o.isEnabled===n&&u(!1);const i={...o,isEnabled:n};return fr(e,i,!0)}if("UPDATE_DROPPABLE_IS_COMBINE_ENABLED"===t.type){if("DROP_PENDING"===e.phase)return e;Qt(e)||u(!1);const{id:r,isCombineEnabled:n}=t.payload,o=e.dimensions.droppables[r];o||u(!1),o.isCombineEnabled===n&&u(!1);const i={...o,isCombineEnabled:n};return fr(e,i,!0)}if("MOVE_BY_WINDOW_SCROLL"===t.type){if("DROP_PENDING"===e.phase||"DROP_ANIMATING"===e.phase)return e;Qt(e)||u(!1),e.isWindowScrollAllowed||u(!1);const r=t.payload.newScroll;if(Le(e.viewport.scroll.current,r))return gr(e);const n=$t(e.viewport,r);return pr(e)?cr({state:e,viewport:n}):ar({state:e,viewport:n})}if("UPDATE_VIEWPORT_MAX_SCROLL"===t.type){if(!Qt(e))return e;const r=t.payload.maxScroll;if(Le(r,e.viewport.scroll.max))return e;const n={...e.viewport,scroll:{...e.viewport.scroll,max:r}};return{...e,viewport:n}}if("MOVE_UP"===t.type||"MOVE_DOWN"===t.type||"MOVE_LEFT"===t.type||"MOVE_RIGHT"===t.type){if("COLLECTING"===e.phase||"DROP_PENDING"===e.phase)return e;"DRAGGING"!==e.phase&&u(!1);const r=Kt({state:e,type:t.type});return r?ar({state:e,impact:r.impact,clientSelection:r.clientSelection,scrollJumpRequest:r.scrollJumpRequest}):e}if("DROP_PENDING"===t.type){const r=t.payload.reason;"COLLECTING"!==e.phase&&u(!1);return{...e,phase:"DROP_PENDING",isWaiting:!0,reason:r}}if("DROP_ANIMATE"===t.type){const{completed:r,dropDuration:n,newHomeClientOffset:o}=t.payload;"DRAGGING"!==e.phase&&"DROP_PENDING"!==e.phase&&u(!1);return{phase:"DROP_ANIMATING",completed:r,dropDuration:n,newHomeClientOffset:o,dimensions:e.dimensions}}if("DROP_COMPLETE"===t.type){const{completed:e}=t.payload;return{phase:"IDLE",completed:e,shouldFlush:!1}}return e};const hr=e=>({type:"LIFT",payload:e}),yr=e=>({type:"PUBLISH_WHILE_DRAGGING",payload:e}),vr=()=>({type:"COLLECTION_STARTING",payload:null}),xr=e=>({type:"UPDATE_DROPPABLE_SCROLL",payload:e}),Ir=e=>({type:"UPDATE_DROPPABLE_IS_ENABLED",payload:e}),Dr=e=>({type:"UPDATE_DROPPABLE_IS_COMBINE_ENABLED",payload:e}),wr=e=>({type:"MOVE",payload:e}),Sr=()=>({type:"MOVE_UP",payload:null}),Er=()=>({type:"MOVE_DOWN",payload:null}),Cr=()=>({type:"MOVE_RIGHT",payload:null}),Or=()=>({type:"MOVE_LEFT",payload:null}),Ar=()=>({type:"FLUSH",payload:null}),Pr=e=>({type:"DROP_COMPLETE",payload:e}),Rr=e=>({type:"DROP",payload:e}),Nr=()=>({type:"DROP_ANIMATION_FINISHED",payload:null});const Br="cubic-bezier(.2,1,.1,1)",Tr={drop:0,combining:.7},Lr={drop:.75},Mr={outOfTheWay:.2,minDropTime:.33,maxDropTime:.55},Gr=`${Mr.outOfTheWay}s ${"cubic-bezier(0.2, 0, 0, 1)"}`,_r={fluid:`opacity ${Gr}`,snap:`transform ${Gr}, opacity ${Gr}`,drop:e=>{const t=`${e}s ${Br}`;return`transform ${t}, opacity ${t}`},outOfTheWay:`transform ${Gr}`,placeholder:`height ${Gr}, width ${Gr}, margin ${Gr}`},$r=e=>Le(e,Ne)?void 0:`translate(${e.x}px, ${e.y}px)`,Fr={moveTo:$r,drop:(e,t)=>{const r=$r(e);if(r)return t?`${r} scale(${Lr.drop})`:r}},{minDropTime:jr,maxDropTime:kr}=Mr,Wr=kr-jr;var Ur=({getState:e,dispatch:t})=>r=>n=>{if("DROP"!==n.type)return void r(n);const o=e(),i=n.payload.reason;if("COLLECTING"===o.phase)return void t((e=>({type:"DROP_PENDING",payload:e}))({reason:i}));if("IDLE"===o.phase)return;"DROP_PENDING"===o.phase&&o.isWaiting&&u(!1),"DRAGGING"!==o.phase&&"DROP_PENDING"!==o.phase&&u(!1);const a=o.critical,s=o.dimensions,l=s.draggables[o.critical.draggable.id],{impact:c,didDropInsideDroppable:d}=(({draggables:e,reason:t,lastImpact:r,home:n,viewport:o,onLiftImpact:i})=>{if(!r.at||"DROP"!==t)return{impact:sr({draggables:e,impact:i,destination:n,viewport:o,forceShouldAnimate:!0}),didDropInsideDroppable:!1};return"REORDER"===r.at.type?{impact:r,didDropInsideDroppable:!0}:{impact:{...r,displaced:gt},didDropInsideDroppable:!0}})({reason:i,lastImpact:o.impact,afterCritical:o.afterCritical,onLiftImpact:o.onLiftImpact,home:o.dimensions.droppables[o.critical.droppable.id],viewport:o.viewport,draggables:o.dimensions.draggables}),p=d?ct(c):null,f=d?dt(c):null,g={index:a.draggable.index,droppableId:a.droppable.id},m={draggableId:l.descriptor.id,type:l.descriptor.type,source:g,reason:i,mode:o.movementMode,destination:p,combine:f},b=(({impact:e,draggable:t,dimensions:r,viewport:n,afterCritical:o})=>{const{draggables:i,droppables:a}=r,s=Xt(e),l=s?a[s]:null,c=a[t.descriptor.droppableId],d=lr({impact:e,draggable:t,draggables:i,afterCritical:o,droppable:l||c,viewport:n});return Te(d,t.client.borderBox.center)})({impact:c,draggable:l,dimensions:s,viewport:o.viewport,afterCritical:o.afterCritical}),h={critical:o.critical,afterCritical:o.afterCritical,result:m,impact:c};if(!(!Le(o.current.client.offset,b)||Boolean(m.combine)))return void t(Pr({completed:h}));const y=(({current:e,destination:t,reason:r})=>{const n=_e(e,t);if(n<=0)return jr;if(n>=1500)return kr;const o=jr+Wr*(n/1500);return Number(("CANCEL"===r?.6*o:o).toFixed(2))})({current:o.current.client.offset,destination:b,reason:i});t((e=>({type:"DROP_ANIMATE",payload:e}))({newHomeClientOffset:b,dropDuration:y,completed:h}))},Hr=function(e){var t=[],r=null,n=function(){for(var n=arguments.length,o=new Array(n),i=0;i<n;i++)o[i]=arguments[i];t=o,r||(r=requestAnimationFrame((function(){r=null,e.apply(void 0,t)})))};return n.cancel=function(){r&&(cancelAnimationFrame(r),r=null)},n},qr=()=>({x:window.pageXOffset,y:window.pageYOffset});function Vr({onWindowScroll:e}){const t=Hr((function(){e(qr())})),r=function(e){return{eventName:"scroll",options:{passive:!0,capture:!1},fn:t=>{t.target!==window&&t.target!==window.document||e()}}}(t);let n=a;function o(){return n!==a}return{start:function(){o()&&u(!1),n=s(window,[r])},stop:function(){o()||u(!1),t.cancel(),n(),n=a},isActive:o}}var zr=e=>{const t=Vr({onWindowScroll:t=>{e.dispatch({type:"MOVE_BY_WINDOW_SCROLL",payload:{newScroll:t}})}});return e=>r=>{t.isActive()||"INITIAL_PUBLISH"!==r.type||t.start(),t.isActive()&&(e=>"DROP_COMPLETE"===e.type||"DROP_ANIMATE"===e.type||"FLUSH"===e.type)(r)&&t.stop(),e(r)}},Yr=()=>{const e=[];return{add:t=>{const r=setTimeout((()=>(t=>{const r=e.findIndex((e=>e.timerId===t));-1===r&&u(!1);const[n]=e.splice(r,1);n.callback()})(r))),n={timerId:r,callback:t};e.push(n)},flush:()=>{if(!e.length)return;const t=[...e];e.length=0,t.forEach((e=>{clearTimeout(e.timerId),e.callback()}))}}};const Jr=(e,t)=>{t()},Xr=(e,t)=>({draggableId:e.draggable.id,type:e.droppable.type,source:{droppableId:e.droppable.id,index:e.draggable.index},mode:t});function Kr(e,t,r,n){if(!e)return void r(n(t));const o=(e=>{let t=!1,r=!1;const n=setTimeout((()=>{r=!0})),o=o=>{t||r||(t=!0,e(o),clearTimeout(n))};return o.wasCalled=()=>t,o})(r);e(t,{announce:o}),o.wasCalled()||r(n(t))}var Qr=(e,t)=>{const r=((e,t)=>{const r=Yr();let n=null;const o=r=>{n||u(!1),n=null,Jr(0,(()=>Kr(e().onDragEnd,r,t,y.onDragEnd)))};return{beforeCapture:(t,r)=>{n&&u(!1),Jr(0,(()=>{const n=e().onBeforeCapture;n&&n({draggableId:t,mode:r})}))},beforeStart:(t,r)=>{n&&u(!1),Jr(0,(()=>{const n=e().onBeforeDragStart;n&&n(Xr(t,r))}))},start:(o,i)=>{n&&u(!1);const a=Xr(o,i);n={mode:i,lastCritical:o,lastLocation:a.source,lastCombine:null},r.add((()=>{Jr(0,(()=>Kr(e().onDragStart,a,t,y.onDragStart)))}))},update:(o,i)=>{const a=ct(i),s=dt(i);n||u(!1);const l=!((e,t)=>{if(e===t)return!0;const r=e.draggable.id===t.draggable.id&&e.draggable.droppableId===t.draggable.droppableId&&e.draggable.type===t.draggable.type&&e.draggable.index===t.draggable.index,n=e.droppable.id===t.droppable.id&&e.droppable.type===t.droppable.type;return r&&n})(o,n.lastCritical);l&&(n.lastCritical=o);const c=(p=a,!(null==(d=n.lastLocation)&&null==p||null!=d&&null!=p&&d.droppableId===p.droppableId&&d.index===p.index));var d,p;c&&(n.lastLocation=a);const f=!((e,t)=>null==e&&null==t||null!=e&&null!=t&&e.draggableId===t.draggableId&&e.droppableId===t.droppableId)(n.lastCombine,s);if(f&&(n.lastCombine=s),!l&&!c&&!f)return;const g={...Xr(o,n.mode),combine:s,destination:a};r.add((()=>{Jr(0,(()=>Kr(e().onDragUpdate,g,t,y.onDragUpdate)))}))},flush:()=>{n||u(!1),r.flush()},drop:o,abort:()=>{if(!n)return;const e={...Xr(n.lastCritical,n.mode),combine:null,destination:null,reason:"CANCEL"};o(e)}}})(e,t);return e=>t=>n=>{if("BEFORE_INITIAL_CAPTURE"===n.type)return void r.beforeCapture(n.payload.draggableId,n.payload.movementMode);if("INITIAL_PUBLISH"===n.type){const e=n.payload.critical;return r.beforeStart(e,n.payload.movementMode),t(n),void r.start(e,n.payload.movementMode)}if("DROP_COMPLETE"===n.type){const e=n.payload.completed.result;return r.flush(),t(n),void r.drop(e)}if(t(n),"FLUSH"===n.type)return void r.abort();const o=e.getState();"DRAGGING"===o.phase&&r.update(o.critical,o.impact)}};var Zr=e=>t=>r=>{if("DROP_ANIMATION_FINISHED"!==r.type)return void t(r);const n=e.getState();"DROP_ANIMATING"!==n.phase&&u(!1),e.dispatch(Pr({completed:n.completed}))};var en=e=>{let t=null,r=null;return n=>o=>{if("FLUSH"!==o.type&&"DROP_COMPLETE"!==o.type&&"DROP_ANIMATION_FINISHED"!==o.type||(r&&(cancelAnimationFrame(r),r=null),t&&(t(),t=null)),n(o),"DROP_ANIMATE"!==o.type)return;const i={eventName:"scroll",options:{capture:!0,passive:!1,once:!0},fn:function(){"DROP_ANIMATING"===e.getState().phase&&e.dispatch({type:"DROP_ANIMATION_FINISHED",payload:null})}};r=requestAnimationFrame((()=>{r=null,t=s(window,[i])}))}};var tn=e=>t=>r=>{if(t(r),"PUBLISH_WHILE_DRAGGING"!==r.type)return;const n=e.getState();"DROP_PENDING"===n.phase&&(n.isWaiting||e.dispatch(Rr({reason:n.reason})))};const rn=R;var nn=({dimensionMarshal:e,focusMarshal:t,styleMarshal:r,getResponders:n,announce:o,autoScroller:i})=>{return O(br,rn(function(){for(var e=arguments.length,t=new Array(e),r=0;r<e;r++)t[r]=arguments[r];return function(e){return function(){var r=e.apply(void 0,arguments),n=function(){throw new Error(w(15))},o={getState:r.getState,dispatch:function(){return n.apply(void 0,arguments)}},i=t.map((function(e){return e(o)}));return n=R.apply(void 0,i)(r.dispatch),D(D({},r),{},{dispatch:n})}}}((a=r,()=>e=>t=>{"INITIAL_PUBLISH"===t.type&&a.dragging(),"DROP_ANIMATE"===t.type&&a.dropping(t.payload.completed.result.reason),"FLUSH"!==t.type&&"DROP_COMPLETE"!==t.type||a.resting(),e(t)}),(e=>()=>t=>r=>{"DROP_COMPLETE"!==r.type&&"FLUSH"!==r.type&&"DROP_ANIMATE"!==r.type||e.stopPublishing(),t(r)})(e),(e=>({getState:t,dispatch:r})=>n=>o=>{if("LIFT"!==o.type)return void n(o);const{id:i,clientSelection:a,movementMode:s}=o.payload,l=t();"DROP_ANIMATING"===l.phase&&r(Pr({completed:l.completed})),"IDLE"!==t().phase&&u(!1),r(Ar()),r({type:"BEFORE_INITIAL_CAPTURE",payload:{draggableId:i,movementMode:s}});const c={draggableId:i,scrollOptions:{shouldPublishImmediately:"SNAP"===s}},{critical:d,dimensions:p,viewport:f}=e.startPublishing(c);r({type:"INITIAL_PUBLISH",payload:{critical:d,dimensions:p,clientSelection:a,movementMode:s,viewport:f}})})(e),Ur,Zr,en,tn,(e=>t=>r=>n=>{if((e=>"DROP_COMPLETE"===e.type||"DROP_ANIMATE"===e.type||"FLUSH"===e.type)(n))return e.stop(),void r(n);if("INITIAL_PUBLISH"===n.type){r(n);const o=t.getState();return"DRAGGING"!==o.phase&&u(!1),void e.start(o)}r(n),e.scroll(t.getState())})(i),zr,(e=>{let t=!1;return()=>r=>n=>{if("INITIAL_PUBLISH"===n.type)return t=!0,e.tryRecordFocus(n.payload.critical.draggable.id),r(n),void e.tryRestoreFocusRecorded();if(r(n),t){if("FLUSH"===n.type)return t=!1,void e.tryRestoreFocusRecorded();if("DROP_COMPLETE"===n.type){t=!1;const r=n.payload.completed.result;r.combine&&e.tryShiftRecord(r.draggableId,r.combine.draggableId),e.tryRestoreFocusRecorded()}}}})(t),Qr(n,o))));var a};var on=({scrollHeight:e,scrollWidth:t,height:r,width:n})=>{const o=Te({x:t,y:e},{x:n,y:r});return{x:Math.max(0,o.x),y:Math.max(0,o.y)}},an=()=>{const e=document.documentElement;return e||u(!1),e},sn=()=>{const e=an();return on({scrollHeight:e.scrollHeight,scrollWidth:e.scrollWidth,width:e.clientWidth,height:e.clientHeight})},ln=({critical:e,scrollOptions:t,registry:r})=>{const n=(()=>{const e=qr(),t=sn(),r=e.y,n=e.x,o=an(),i=o.clientWidth,a=o.clientHeight;return{frame:je({top:r,left:n,right:n+i,bottom:r+a}),scroll:{initial:e,current:e,max:t,diff:{value:Ne,displacement:Ne}}}})(),o=n.scroll.current,i=e.droppable,a=r.droppable.getAllByType(i.type).map((e=>e.callbacks.getDimensionAndWatchScroll(o,t))),s=r.draggable.getAllByType(e.draggable.type).map((e=>e.getDimension(o)));return{dimensions:{draggables:it(s),droppables:ot(a)},critical:e,viewport:n}};function cn(e,t,r){if(r.descriptor.id===t.id)return!1;if(r.descriptor.type!==t.type)return!1;return"virtual"===e.droppable.getById(r.descriptor.droppableId).descriptor.mode}var dn=(e,t)=>{let r=null;const n=function({registry:e,callbacks:t}){let r={additions:{},removals:{},modified:{}},n=null;const o=()=>{n||(t.collectionStarting(),n=requestAnimationFrame((()=>{n=null;const{additions:o,removals:i,modified:a}=r,s=Object.keys(o).map((t=>e.draggable.getById(t).getDimension(Ne))).sort(((e,t)=>e.descriptor.index-t.descriptor.index)),l=Object.keys(a).map((t=>({droppableId:t,scroll:e.droppable.getById(t).callbacks.getScrollWhileDragging()}))),c={additions:s,removals:Object.keys(i),modified:l};r={additions:{},removals:{},modified:{}},t.publish(c)})))};return{add:e=>{const t=e.descriptor.id;r.additions[t]=e,r.modified[e.descriptor.droppableId]=!0,r.removals[t]&&delete r.removals[t],o()},remove:e=>{const t=e.descriptor;r.removals[t.id]=!0,r.modified[t.droppableId]=!0,r.additions[t.id]&&delete r.additions[t.id],o()},stop:()=>{n&&(cancelAnimationFrame(n),n=null,r={additions:{},removals:{},modified:{}})}}}({callbacks:{publish:t.publishWhileDragging,collectionStarting:t.collectionStarting},registry:e}),o=t=>{r||u(!1);const o=r.critical.draggable;"ADDITION"===t.type&&cn(e,o,t.value)&&n.add(t.value),"REMOVAL"===t.type&&cn(e,o,t.value)&&n.remove(t.value)},i={updateDroppableIsEnabled:(n,o)=>{e.droppable.exists(n)||u(!1),r&&t.updateDroppableIsEnabled({id:n,isEnabled:o})},updateDroppableIsCombineEnabled:(n,o)=>{r&&(e.droppable.exists(n)||u(!1),t.updateDroppableIsCombineEnabled({id:n,isCombineEnabled:o}))},scrollDroppable:(t,n)=>{r&&e.droppable.getById(t).callbacks.scroll(n)},updateDroppableScroll:(n,o)=>{r&&(e.droppable.exists(n)||u(!1),t.updateDroppableScroll({id:n,newScroll:o}))},startPublishing:t=>{r&&u(!1);const n=e.draggable.getById(t.draggableId),i=e.droppable.getById(n.descriptor.droppableId),a={draggable:n.descriptor,droppable:i.descriptor},s=e.subscribe(o);return r={critical:a,unsubscribe:s},ln({critical:a,registry:e,scrollOptions:t.scrollOptions})},stopPublishing:()=>{if(!r)return;n.stop();const t=r.critical.droppable;e.droppable.getAllByType(t.type).forEach((e=>e.callbacks.dragStopped())),r.unsubscribe(),r=null}};return i},un=(e,t)=>"IDLE"===e.phase||"DROP_ANIMATING"===e.phase&&(e.completed.result.draggableId!==t&&"DROP"===e.completed.result.reason),pn=e=>{window.scrollBy(e.x,e.y)};const fn=nt((e=>at(e).filter((e=>!!e.isEnabled&&!!e.frame))));var gn=({center:e,destination:t,droppables:r})=>{if(t){const e=r[t];return e.frame?e:null}const n=((e,t)=>{const r=fn(t).find((t=>(t.frame||u(!1),Zt(t.frame.pageMarginBox)(e))))||null;return r})(e,r);return n};const mn={startFromPercentage:.25,maxScrollAtPercentage:.05,maxPixelScroll:28,ease:e=>e**2,durationDampening:{stopDampeningAt:1200,accelerateAt:360},disabled:!1};var bn=({startOfRange:e,endOfRange:t,current:r})=>{const n=t-e;if(0===n)return 0;return(r-e)/n},hn=({distanceToEdge:e,thresholds:t,dragStartTime:r,shouldUseTimeDampening:n,getAutoScrollerOptions:o})=>{const i=((e,t,r=(()=>mn))=>{const n=r();if(e>t.startScrollingFrom)return 0;if(e<=t.maxScrollValueAt)return n.maxPixelScroll;if(e===t.startScrollingFrom)return 1;const o=1-bn({startOfRange:t.maxScrollValueAt,endOfRange:t.startScrollingFrom,current:e}),i=n.maxPixelScroll*n.ease(o);return Math.ceil(i)})(e,t,o);return 0===i?0:n?Math.max(((e,t,r)=>{const n=r(),o=n.durationDampening.accelerateAt,i=n.durationDampening.stopDampeningAt,a=t,s=i,l=Date.now()-a;if(l>=i)return e;if(l<o)return 1;const c=bn({startOfRange:o,endOfRange:s,current:l}),d=e*n.ease(c);return Math.ceil(d)})(i,r,o),1):i},yn=({container:e,distanceToEdges:t,dragStartTime:r,axis:n,shouldUseTimeDampening:o,getAutoScrollerOptions:i})=>{const a=((e,t,r=(()=>mn))=>{const n=r();return{startScrollingFrom:e[t.size]*n.startFromPercentage,maxScrollValueAt:e[t.size]*n.maxScrollAtPercentage}})(e,n,i);return t[n.end]<t[n.start]?hn({distanceToEdge:t[n.end],thresholds:a,dragStartTime:r,shouldUseTimeDampening:o,getAutoScrollerOptions:i}):-1*hn({distanceToEdge:t[n.start],thresholds:a,dragStartTime:r,shouldUseTimeDampening:o,getAutoScrollerOptions:i})};const vn=Fe((e=>0===e?0:e));var xn=({dragStartTime:e,container:t,subject:r,center:n,shouldUseTimeDampening:o,getAutoScrollerOptions:i})=>{const a={top:n.y-t.top,right:t.right-n.x,bottom:t.bottom-n.y,left:n.x-t.left},s=yn({container:t,distanceToEdges:a,dragStartTime:e,axis:vt,shouldUseTimeDampening:o,getAutoScrollerOptions:i}),l=yn({container:t,distanceToEdges:a,dragStartTime:e,axis:xt,shouldUseTimeDampening:o,getAutoScrollerOptions:i}),c=vn({x:l,y:s});if(Le(c,Ne))return null;const d=(({container:e,subject:t,proposedScroll:r})=>{const n=t.height>e.height,o=t.width>e.width;return o||n?o&&n?null:{x:o?0:r.x,y:n?0:r.y}:r})({container:t,subject:r,proposedScroll:c});return d?Le(d,Ne)?null:d:null};const In=Fe((e=>0===e?0:e>0?1:-1)),Dn=(()=>{const e=(e,t)=>e<0?e:e>t?e-t:0;return({current:t,max:r,change:n})=>{const o=Be(t,n),i={x:e(o.x,r.x),y:e(o.y,r.y)};return Le(i,Ne)?null:i}})(),wn=({max:e,current:t,change:r})=>{const n={x:Math.max(t.x,e.x),y:Math.max(t.y,e.y)},o=In(r),i=Dn({max:n,current:t,change:o});return!i||(0!==o.x&&0===i.x||0!==o.y&&0===i.y)},Sn=(e,t)=>wn({current:e.scroll.current,max:e.scroll.max,change:t}),En=(e,t)=>{const r=e.frame;return!!r&&wn({current:r.scroll.current,max:r.scroll.max,change:t})};var Cn=({state:e,dragStartTime:t,shouldUseTimeDampening:r,scrollWindow:n,scrollDroppable:o,getAutoScrollerOptions:i})=>{const a=e.current.page.borderBoxCenter,s=e.dimensions.draggables[e.critical.draggable.id].page.marginBox;if(e.isWindowScrollAllowed){const o=(({viewport:e,subject:t,center:r,dragStartTime:n,shouldUseTimeDampening:o,getAutoScrollerOptions:i})=>{const a=xn({dragStartTime:n,container:e.frame,subject:t,center:r,shouldUseTimeDampening:o,getAutoScrollerOptions:i});return a&&Sn(e,a)?a:null})({dragStartTime:t,viewport:e.viewport,subject:s,center:a,shouldUseTimeDampening:r,getAutoScrollerOptions:i});if(o)return void n(o)}const l=gn({center:a,destination:Xt(e.impact),droppables:e.dimensions.droppables});if(!l)return;const c=(({droppable:e,subject:t,center:r,dragStartTime:n,shouldUseTimeDampening:o,getAutoScrollerOptions:i})=>{const a=e.frame;if(!a)return null;const s=xn({dragStartTime:n,container:a.pageMarginBox,subject:t,center:r,shouldUseTimeDampening:o,getAutoScrollerOptions:i});return s&&En(e,s)?s:null})({dragStartTime:t,droppable:l,subject:s,center:a,shouldUseTimeDampening:r,getAutoScrollerOptions:i});c&&o(l.descriptor.id,c)},On=({scrollWindow:e,scrollDroppable:t,getAutoScrollerOptions:r=(()=>mn)})=>{const n=Hr(e),o=Hr(t);let i=null;const a=e=>{i||u(!1);const{shouldUseTimeDampening:t,dragStartTime:a}=i;Cn({state:e,scrollWindow:n,scrollDroppable:o,dragStartTime:a,shouldUseTimeDampening:t,getAutoScrollerOptions:r})};return{start:e=>{i&&u(!1);const t=Date.now();let n=!1;const o=()=>{n=!0};Cn({state:e,dragStartTime:0,shouldUseTimeDampening:!1,scrollWindow:o,scrollDroppable:o,getAutoScrollerOptions:r}),i={dragStartTime:t,shouldUseTimeDampening:n},n&&a(e)},stop:()=>{i&&(n.cancel(),o.cancel(),i=null)},scroll:a}},An=({move:e,scrollDroppable:t,scrollWindow:r})=>{const n=(e,r)=>{if(!En(e,r))return r;const n=((e,t)=>{const r=e.frame;return r&&En(e,t)?Dn({current:r.scroll.current,max:r.scroll.max,change:t}):null})(e,r);if(!n)return t(e.descriptor.id,r),null;const o=Te(r,n);t(e.descriptor.id,o);return Te(r,o)},o=(e,t,n)=>{if(!e)return n;if(!Sn(t,n))return n;const o=((e,t)=>{if(!Sn(e,t))return null;const r=e.scroll.max,n=e.scroll.current;return Dn({current:n,max:r,change:t})})(t,n);if(!o)return r(n),null;const i=Te(n,o);r(i);return Te(n,i)};return t=>{const r=t.scrollJumpRequest;if(!r)return;const i=Xt(t.impact);i||u(!1);const a=n(t.dimensions.droppables[i],r);if(!a)return;const s=t.viewport,l=o(t.isWindowScrollAllowed,s,a);l&&((t,r)=>{const n=Be(t.current.client.selection,r);e({client:n})})(t,l)}},Pn=({scrollDroppable:e,scrollWindow:t,move:r,getAutoScrollerOptions:n})=>{const o=On({scrollWindow:t,scrollDroppable:e,getAutoScrollerOptions:n}),i=An({move:r,scrollWindow:t,scrollDroppable:e});return{scroll:e=>{n().disabled||"DRAGGING"!==e.phase||("FLUID"!==e.movementMode?e.scrollJumpRequest&&i(e):o.scroll(e))},start:o.start,stop:o.stop}};const Rn="data-rfd",Nn=(()=>{const e=`${Rn}-drag-handle`;return{base:e,draggableId:`${e}-draggable-id`,contextId:`${e}-context-id`}})(),Bn=(()=>{const e=`${Rn}-draggable`;return{base:e,contextId:`${e}-context-id`,id:`${e}-id`}})(),Tn=(()=>{const e=`${Rn}-droppable`;return{base:e,contextId:`${e}-context-id`,id:`${e}-id`}})(),Ln={contextId:`${Rn}-scroll-container-context-id`},Mn=(e,t)=>e.map((e=>{const r=e.styles[t];return r?`${e.selector} { ${r} }`:""})).join(" ");var Gn=e=>{const t=(r=e,e=>`[${e}="${r}"]`);var r;const n=(()=>{const e="\n      cursor: -webkit-grab;\n      cursor: grab;\n    ";return{selector:t(Nn.contextId),styles:{always:"\n          -webkit-touch-callout: none;\n          -webkit-tap-highlight-color: rgba(0,0,0,0);\n          touch-action: manipulation;\n        ",resting:e,dragging:"pointer-events: none;",dropAnimating:e}}})(),o=[(()=>{const e=`\n      transition: ${_r.outOfTheWay};\n    `;return{selector:t(Bn.contextId),styles:{dragging:e,dropAnimating:e,userCancel:e}}})(),n,{selector:t(Tn.contextId),styles:{always:"overflow-anchor: none;"}},{selector:"body",styles:{dragging:"\n        cursor: grabbing;\n        cursor: -webkit-grabbing;\n        user-select: none;\n        -webkit-user-select: none;\n        -moz-user-select: none;\n        -ms-user-select: none;\n        overflow-anchor: none;\n      "}}];return{always:Mn(o,"always"),resting:Mn(o,"resting"),dragging:Mn(o,"dragging"),dropAnimating:Mn(o,"dropAnimating"),userCancel:Mn(o,"userCancel")}};var _n="undefined"!=typeof window&&void 0!==window.document&&void 0!==window.document.createElement?t.useLayoutEffect:t.useEffect;const $n=()=>{const e=document.querySelector("head");return e||u(!1),e},Fn=e=>{const t=document.createElement("style");return e&&t.setAttribute("nonce",e),t.type="text/css",t};function jn(e,t){return Array.from(e.querySelectorAll(t))}var kn=e=>e&&e.ownerDocument&&e.ownerDocument.defaultView?e.ownerDocument.defaultView:window;function Wn(e){return e instanceof kn(e).HTMLElement}function Un(e){const r=t.useRef({}),n=t.useRef(null),o=t.useRef(null),i=t.useRef(!1),a=Re((function(e,t){const n={id:e,focus:t};return r.current[e]=n,function(){const t=r.current;t[e]!==n&&delete t[e]}}),[]),s=Re((function(t){const r=function(e,t){const r=`[${Nn.contextId}="${e}"]`,n=jn(document,r);if(!n.length)return null;const o=n.find((e=>e.getAttribute(Nn.draggableId)===t));return o&&Wn(o)?o:null}(e,t);r&&r!==document.activeElement&&r.focus()}),[e]),l=Re((function(e,t){n.current===e&&(n.current=t)}),[]),c=Re((function(){o.current||i.current&&(o.current=requestAnimationFrame((()=>{o.current=null;const e=n.current;e&&s(e)})))}),[s]),d=Re((function(e){n.current=null;const t=document.activeElement;t&&t.getAttribute(Nn.draggableId)===e&&(n.current=e)}),[]);_n((()=>(i.current=!0,function(){i.current=!1;const e=o.current;e&&cancelAnimationFrame(e)})),[]);return Pe((()=>({register:a,tryRecordFocus:d,tryRestoreFocusRecorded:c,tryShiftRecord:l})),[a,d,c,l])}function Hn(){const e={draggables:{},droppables:{}},t=[];function r(e){t.length&&t.forEach((t=>t(e)))}function n(t){return e.draggables[t]||null}function o(t){return e.droppables[t]||null}return{draggable:{register:t=>{e.draggables[t.descriptor.id]=t,r({type:"ADDITION",value:t})},update:(t,r)=>{const n=e.draggables[r.descriptor.id];n&&n.uniqueId===t.uniqueId&&(delete e.draggables[r.descriptor.id],e.draggables[t.descriptor.id]=t)},unregister:t=>{const o=t.descriptor.id,i=n(o);i&&t.uniqueId===i.uniqueId&&(delete e.draggables[o],e.droppables[t.descriptor.droppableId]&&r({type:"REMOVAL",value:t}))},getById:function(e){const t=n(e);return t||u(!1),t},findById:n,exists:e=>Boolean(n(e)),getAllByType:t=>Object.values(e.draggables).filter((e=>e.descriptor.type===t))},droppable:{register:t=>{e.droppables[t.descriptor.id]=t},unregister:t=>{const r=o(t.descriptor.id);r&&t.uniqueId===r.uniqueId&&delete e.droppables[t.descriptor.id]},getById:function(e){const t=o(e);return t||u(!1),t},findById:o,exists:e=>Boolean(o(e)),getAllByType:t=>Object.values(e.droppables).filter((e=>e.descriptor.type===t))},subscribe:function(e){return t.push(e),function(){const r=t.indexOf(e);-1!==r&&t.splice(r,1)}},clean:function(){e.draggables={},e.droppables={},t.length=0}}}var qn=t.createContext(null),Vn=()=>{const e=document.body;return e||u(!1),e};var zn={position:"absolute",width:"1px",height:"1px",margin:"-1px",border:"0",padding:"0",overflow:"hidden",clip:"rect(0 0 0 0)","clip-path":"inset(100%)"};const Yn=e=>`rfd-announcement-${e}`;let Jn=0;const Xn={separator:"::"};var Kn="useId"in t?function(e,r=Xn){const n=t.useId();return Pe((()=>`${e}${r.separator}${n}`),[r.separator,e,n])}:function(e,t=Xn){return Pe((()=>`${e}${t.separator}${Jn++}`),[t.separator,e])};var Qn=t.createContext(null);function Zn(e){const r=t.useRef(e);return t.useEffect((()=>{r.current=e})),r}function eo(e){return"IDLE"!==e.phase&&"DROP_ANIMATING"!==e.phase&&e.isDragging}const to=27,ro=32,no=37,oo=38,io=39,ao=40,so={13:!0,9:!0};var lo=e=>{so[e.keyCode]&&e.preventDefault()};var co=(()=>{const e="visibilitychange";if("undefined"==typeof document)return e;return[e,`ms${e}`,`webkit${e}`,`moz${e}`,`o${e}`].find((e=>`on${e}`in document))||e})();const uo=0,po=5;const fo={type:"IDLE"};function go({cancel:e,completed:t,getPhase:r,setPhase:n}){return[{eventName:"mousemove",fn:e=>{const{button:t,clientX:o,clientY:i}=e;if(t!==uo)return;const a={x:o,y:i},s=r();if("DRAGGING"===s.type)return e.preventDefault(),void s.actions.move(a);"PENDING"!==s.type&&u(!1);const l=s.point;if(c=l,d=a,!(Math.abs(d.x-c.x)>=po||Math.abs(d.y-c.y)>=po))return;var c,d;e.preventDefault();const p=s.actions.fluidLift(a);n({type:"DRAGGING",actions:p})}},{eventName:"mouseup",fn:n=>{const o=r();"DRAGGING"===o.type?(n.preventDefault(),o.actions.drop({shouldBlockNextClick:!0}),t()):e()}},{eventName:"mousedown",fn:t=>{"DRAGGING"===r().type&&t.preventDefault(),e()}},{eventName:"keydown",fn:t=>{if("PENDING"!==r().type)return t.keyCode===to?(t.preventDefault(),void e()):void lo(t);e()}},{eventName:"resize",fn:e},{eventName:"scroll",options:{passive:!0,capture:!1},fn:()=>{"PENDING"===r().type&&e()}},{eventName:"webkitmouseforcedown",fn:t=>{const n=r();"IDLE"===n.type&&u(!1),n.actions.shouldRespectForcePress()?e():t.preventDefault()}},{eventName:co,fn:e}]}function mo(e){const r=t.useRef(fo),n=t.useRef(a),o=Pe((()=>({eventName:"mousedown",fn:function(t){if(t.defaultPrevented)return;if(t.button!==uo)return;if(t.ctrlKey||t.metaKey||t.shiftKey||t.altKey)return;const r=e.findClosestDraggableId(t);if(!r)return;const o=e.tryGetLock(r,c,{sourceEvent:t});if(!o)return;t.preventDefault();const i={x:t.clientX,y:t.clientY};n.current(),f(o,i)}})),[e]),i=Pe((()=>({eventName:"webkitmouseforcewillbegin",fn:t=>{if(t.defaultPrevented)return;const r=e.findClosestDraggableId(t);if(!r)return;const n=e.findOptionsForDraggable(r);n&&(n.shouldRespectForcePress||e.canGetLock(r)&&t.preventDefault())}})),[e]),l=Re((function(){n.current=s(window,[i,o],{passive:!1,capture:!0})}),[i,o]),c=Re((()=>{"IDLE"!==r.current.type&&(r.current=fo,n.current(),l())}),[l]),d=Re((()=>{const e=r.current;c(),"DRAGGING"===e.type&&e.actions.cancel({shouldBlockNextClick:!0}),"PENDING"===e.type&&e.actions.abort()}),[c]),p=Re((function(){const e=go({cancel:d,completed:c,getPhase:()=>r.current,setPhase:e=>{r.current=e}});n.current=s(window,e,{capture:!0,passive:!1})}),[d,c]),f=Re((function(e,t){"IDLE"!==r.current.type&&u(!1),r.current={type:"PENDING",point:t,actions:e},p()}),[p]);_n((function(){return l(),function(){n.current()}}),[l])}function bo(){}const ho={34:!0,33:!0,36:!0,35:!0};function yo(e,t){function r(){t(),e.cancel()}return[{eventName:"keydown",fn:n=>n.keyCode===to?(n.preventDefault(),void r()):n.keyCode===ro?(n.preventDefault(),t(),void e.drop()):n.keyCode===ao?(n.preventDefault(),void e.moveDown()):n.keyCode===oo?(n.preventDefault(),void e.moveUp()):n.keyCode===io?(n.preventDefault(),void e.moveRight()):n.keyCode===no?(n.preventDefault(),void e.moveLeft()):void(ho[n.keyCode]?n.preventDefault():lo(n))},{eventName:"mousedown",fn:r},{eventName:"mouseup",fn:r},{eventName:"click",fn:r},{eventName:"touchstart",fn:r},{eventName:"resize",fn:r},{eventName:"wheel",fn:r,options:{passive:!0}},{eventName:co,fn:r}]}function vo(e){const r=t.useRef(bo),n=Pe((()=>({eventName:"keydown",fn:function(t){if(t.defaultPrevented)return;if(t.keyCode!==ro)return;const n=e.findClosestDraggableId(t);if(!n)return;const i=e.tryGetLock(n,c,{sourceEvent:t});if(!i)return;t.preventDefault();let a=!0;const l=i.snapLift();function c(){a||u(!1),a=!1,r.current(),o()}r.current(),r.current=s(window,yo(l,c),{capture:!0,passive:!1})}})),[e]),o=Re((function(){r.current=s(window,[n],{passive:!1,capture:!0})}),[n]);_n((function(){return o(),function(){r.current()}}),[o])}const xo={type:"IDLE"},Io=.15;function Do(e){const r=t.useRef(xo),n=t.useRef(a),o=Re((function(){return r.current}),[]),i=Re((function(e){r.current=e}),[]),l=Pe((()=>({eventName:"touchstart",fn:function(t){if(t.defaultPrevented)return;const r=e.findClosestDraggableId(t);if(!r)return;const o=e.tryGetLock(r,d,{sourceEvent:t});if(!o)return;const i=t.touches[0],{clientX:a,clientY:s}=i,l={x:a,y:s};n.current(),m(o,l)}})),[e]),c=Re((function(){n.current=s(window,[l],{capture:!0,passive:!1})}),[l]),d=Re((()=>{const e=r.current;"IDLE"!==e.type&&("PENDING"===e.type&&clearTimeout(e.longPressTimerId),i(xo),n.current(),c())}),[c,i]),p=Re((()=>{const e=r.current;d(),"DRAGGING"===e.type&&e.actions.cancel({shouldBlockNextClick:!0}),"PENDING"===e.type&&e.actions.abort()}),[d]),f=Re((function(){const e={capture:!0,passive:!1},t={cancel:p,completed:d,getPhase:o},r=s(window,function({cancel:e,completed:t,getPhase:r}){return[{eventName:"touchmove",options:{capture:!1},fn:t=>{const n=r();if("DRAGGING"!==n.type)return void e();n.hasMoved=!0;const{clientX:o,clientY:i}=t.touches[0],a={x:o,y:i};t.preventDefault(),n.actions.move(a)}},{eventName:"touchend",fn:n=>{const o=r();"DRAGGING"===o.type?(n.preventDefault(),o.actions.drop({shouldBlockNextClick:!0}),t()):e()}},{eventName:"touchcancel",fn:t=>{"DRAGGING"===r().type?(t.preventDefault(),e()):e()}},{eventName:"touchforcechange",fn:t=>{const n=r();"IDLE"===n.type&&u(!1);const o=t.touches[0];if(!o)return;if(!(o.force>=Io))return;const i=n.actions.shouldRespectForcePress();if("PENDING"!==n.type)return i?n.hasMoved?void t.preventDefault():void e():void t.preventDefault();i&&e()}},{eventName:co,fn:e}]}(t),e),i=s(window,function({cancel:e,getPhase:t}){return[{eventName:"orientationchange",fn:e},{eventName:"resize",fn:e},{eventName:"contextmenu",fn:e=>{e.preventDefault()}},{eventName:"keydown",fn:r=>{"DRAGGING"===t().type?(r.keyCode===to&&r.preventDefault(),e()):e()}},{eventName:co,fn:e}]}(t),e);n.current=function(){r(),i()}}),[p,o,d]),g=Re((function(){const e=o();"PENDING"!==e.type&&u(!1);const t=e.actions.fluidLift(e.point);i({type:"DRAGGING",actions:t,hasMoved:!1})}),[o,i]),m=Re((function(e,t){"IDLE"!==o().type&&u(!1);const r=setTimeout(g,120);i({type:"PENDING",point:t,actions:e,longPressTimerId:r}),f()}),[f,o,i,g]);_n((function(){return c(),function(){n.current();const e=o();"PENDING"===e.type&&(clearTimeout(e.longPressTimerId),i(xo))}}),[o,c,i]),_n((function(){return s(window,[{eventName:"touchmove",fn:()=>{},options:{capture:!1,passive:!1}}])}),[])}const wo=["input","button","textarea","select","option","optgroup","video","audio"];function So(e,t){if(null==t)return!1;if(wo.includes(t.tagName.toLowerCase()))return!0;const r=t.getAttribute("contenteditable");return"true"===r||""===r||t!==e&&So(e,t.parentElement)}function Eo(e,t){const r=t.target;return!!Wn(r)&&So(e,r)}var Co=e=>je(e.getBoundingClientRect()).center;const Oo=(()=>{const e="matches";if("undefined"==typeof document)return e;return[e,"msMatchesSelector","webkitMatchesSelector"].find((e=>e in Element.prototype))||e})();function Ao(e,t){return null==e?null:e[Oo](t)?e:Ao(e.parentElement,t)}function Po(e,t){return e.closest?e.closest(t):Ao(e,t)}function Ro(e,t){const r=t.target;if(!((n=r)instanceof kn(n).Element))return null;var n;const o=function(e){return`[${Nn.contextId}="${e}"]`}(e),i=Po(r,o);return i&&Wn(i)?i:null}function No(e){e.preventDefault()}function Bo({expected:e,phase:t,isLockActive:r,shouldWarn:n}){return!!r()&&e===t}function To({lockAPI:e,store:t,registry:r,draggableId:n}){if(e.isClaimed())return!1;const o=r.draggable.findById(n);return!!o&&(!!o.options.isEnabled&&!!un(t.getState(),n))}function Lo({lockAPI:e,contextId:t,store:r,registry:n,draggableId:o,forceSensorStop:i,sourceEvent:l}){if(!To({lockAPI:e,store:r,registry:n,draggableId:o}))return null;const c=n.draggable.getById(o),d=function(e,t){const r=`[${Bn.contextId}="${e}"]`,n=jn(document,r).find((e=>e.getAttribute(Bn.id)===t));return n&&Wn(n)?n:null}(t,c.descriptor.id);if(!d)return null;if(l&&!c.options.canDragInteractiveElements&&Eo(d,l))return null;const p=e.claim(i||a);let f="PRE_DRAG";function g(){return c.options.shouldRespectForcePress}function m(){return e.isActive(p)}const b=function(e,t){Bo({expected:e,phase:f,isLockActive:m,shouldWarn:!0})&&r.dispatch(t())}.bind(null,"DRAGGING");function h(t){function n(){e.release(),f="COMPLETED"}function o(e,o={shouldBlockNextClick:!1}){if(t.cleanup(),o.shouldBlockNextClick){const e=s(window,[{eventName:"click",fn:No,options:{once:!0,passive:!1,capture:!0}}]);setTimeout(e)}n(),r.dispatch(Rr({reason:e}))}return"PRE_DRAG"!==f&&(n(),u(!1)),r.dispatch(hr(t.liftActionArgs)),f="DRAGGING",{isActive:()=>Bo({expected:"DRAGGING",phase:f,isLockActive:m,shouldWarn:!1}),shouldRespectForcePress:g,drop:e=>o("DROP",e),cancel:e=>o("CANCEL",e),...t.actions}}return{isActive:()=>Bo({expected:"PRE_DRAG",phase:f,isLockActive:m,shouldWarn:!1}),shouldRespectForcePress:g,fluidLift:function(e){const t=Hr((e=>{b((()=>wr({client:e})))}));return{...h({liftActionArgs:{id:o,clientSelection:e,movementMode:"FLUID"},cleanup:()=>t.cancel(),actions:{move:t}}),move:t}},snapLift:function(){const e={moveUp:()=>b(Sr),moveRight:()=>b(Cr),moveDown:()=>b(Er),moveLeft:()=>b(Or)};return h({liftActionArgs:{id:o,clientSelection:Co(d),movementMode:"SNAP"},cleanup:a,actions:e})},abort:function(){Bo({expected:"PRE_DRAG",phase:f,isLockActive:m,shouldWarn:!0})&&e.release()}}}const Mo=[mo,vo,Do];function Go({contextId:e,store:r,registry:n,customSensors:o,enableDefaultSensors:i}){const a=[...i?Mo:[],...o||[]],s=t.useState((()=>function(){let e=null;function t(){e||u(!1),e=null}return{isClaimed:function(){return Boolean(e)},isActive:function(t){return t===e},claim:function(t){e&&u(!1);const r={abandon:t};return e=r,r},release:t,tryAbandon:function(){e&&(e.abandon(),t())}}}()))[0],l=Re((function(e,t){eo(e)&&!eo(t)&&s.tryAbandon()}),[s]);_n((function(){let e=r.getState();return r.subscribe((()=>{const t=r.getState();l(e,t),e=t}))}),[s,r,l]),_n((()=>s.tryAbandon),[s.tryAbandon]);const c=Re((e=>To({lockAPI:s,registry:n,store:r,draggableId:e})),[s,n,r]),d=Re(((t,o,i)=>Lo({lockAPI:s,registry:n,contextId:e,store:r,draggableId:t,forceSensorStop:o||null,sourceEvent:i&&i.sourceEvent?i.sourceEvent:null})),[e,s,n,r]),p=Re((t=>function(e,t){const r=Ro(e,t);return r?r.getAttribute(Nn.draggableId):null}(e,t)),[e]),f=Re((e=>{const t=n.draggable.findById(e);return t?t.options:null}),[n.draggable]),g=Re((function(){s.isClaimed()&&(s.tryAbandon(),"IDLE"!==r.getState().phase&&r.dispatch(Ar()))}),[s,r]),m=Re((()=>s.isClaimed()),[s]),b=Pe((()=>({canGetLock:c,tryGetLock:d,findClosestDraggableId:p,findOptionsForDraggable:f,tryReleaseLock:g,isLockClaimed:m})),[c,d,p,f,g,m]);for(let e=0;e<a.length;e++)a[e](b)}const _o=e=>({onBeforeCapture:n=>{const o=()=>{e.onBeforeCapture&&e.onBeforeCapture(n)};t.version.startsWith("16")||t.version.startsWith("17")?o():r.flushSync(o)},onBeforeDragStart:e.onBeforeDragStart,onDragStart:e.onDragStart,onDragEnd:e.onDragEnd,onDragUpdate:e.onDragUpdate}),$o=e=>({...mn,...e.autoScrollerOptions,durationDampening:{...mn.durationDampening,...e.autoScrollerOptions}});function Fo(e){return e.current||u(!1),e.current}function jo(e){const{contextId:r,setCallbacks:n,sensors:o,nonce:i,dragHandleUsageInstructions:a}=e,s=t.useRef(null),l=Zn(e),c=Re((()=>_o(l.current)),[l]),d=Re((()=>$o(l.current)),[l]),p=function(e){const r=Pe((()=>Yn(e)),[e]),n=t.useRef(null);return t.useEffect((function(){const e=document.createElement("div");return n.current=e,e.id=r,e.setAttribute("aria-live","assertive"),e.setAttribute("aria-atomic","true"),_(e.style,zn),Vn().appendChild(e),function(){setTimeout((function(){const t=Vn();t.contains(e)&&t.removeChild(e),e===n.current&&(n.current=null)}))}}),[r]),Re((e=>{const t=n.current;t&&(t.textContent=e)}),[])}(r),f=function({contextId:e,text:r}){const n=Kn("hidden-text",{separator:"-"}),o=Pe((()=>function({contextId:e,uniqueId:t}){return`rfd-hidden-text-${e}-${t}`}({contextId:e,uniqueId:n})),[n,e]);return t.useEffect((function(){const e=document.createElement("div");return e.id=o,e.textContent=r,e.style.display="none",Vn().appendChild(e),function(){const t=Vn();t.contains(e)&&t.removeChild(e)}}),[o,r]),o}({contextId:r,text:a}),g=function(e,r){const n=Pe((()=>Gn(e)),[e]),o=t.useRef(null),i=t.useRef(null),a=Re(nt((e=>{const t=i.current;t||u(!1),t.textContent=e})),[]),s=Re((e=>{const t=o.current;t||u(!1),t.textContent=e}),[]);_n((()=>{(o.current||i.current)&&u(!1);const t=Fn(r),l=Fn(r);return o.current=t,i.current=l,t.setAttribute(`${Rn}-always`,e),l.setAttribute(`${Rn}-dynamic`,e),$n().appendChild(t),$n().appendChild(l),s(n.always),a(n.resting),()=>{const e=e=>{const t=e.current;t||u(!1),$n().removeChild(t),e.current=null};e(o),e(i)}}),[r,s,a,n.always,n.resting,e]);const l=Re((()=>a(n.dragging)),[a,n.dragging]),c=Re((e=>{a("DROP"!==e?n.userCancel:n.dropAnimating)}),[a,n.dropAnimating,n.userCancel]),d=Re((()=>{i.current&&a(n.resting)}),[a,n.resting]);return Pe((()=>({dragging:l,dropping:c,resting:d})),[l,c,d])}(r,i),m=Re((e=>{Fo(s).dispatch(e)}),[]),b=Pe((()=>P({publishWhileDragging:yr,updateDroppableScroll:xr,updateDroppableIsEnabled:Ir,updateDroppableIsCombineEnabled:Dr,collectionStarting:vr},m)),[m]),h=function(){const e=Pe(Hn,[]);return t.useEffect((()=>function(){t.version.startsWith("16")||t.version.startsWith("17")?requestAnimationFrame(e.clean):e.clean()}),[e]),e}(),y=Pe((()=>dn(h,b)),[h,b]),v=Pe((()=>Pn({scrollWindow:pn,scrollDroppable:y.scrollDroppable,getAutoScrollerOptions:d,...P({move:wr},m)})),[y.scrollDroppable,m,d]),x=Un(r),I=Pe((()=>nn({announce:p,autoScroller:v,dimensionMarshal:y,focusMarshal:x,getResponders:c,styleMarshal:g})),[p,v,y,x,c,g]);s.current=I;const D=Re((()=>{const e=Fo(s);"IDLE"!==e.getState().phase&&e.dispatch(Ar())}),[]),w=Re((()=>{const e=Fo(s).getState();return"DROP_ANIMATING"===e.phase||"IDLE"!==e.phase&&e.isDragging}),[]);n(Pe((()=>({isDragging:w,tryAbort:D})),[w,D]));const S=Re((e=>un(Fo(s).getState(),e)),[]),E=Re((()=>Qt(Fo(s).getState())),[]),C=Pe((()=>({marshal:y,focus:x,contextId:r,canLift:S,isMovementAllowed:E,dragHandleUsageInstructionsId:f,registry:h})),[r,y,f,x,S,E,h]);return Go({contextId:r,store:I,registry:h,customSensors:o||null,enableDefaultSensors:!1!==e.enableDefaultSensors}),t.useEffect((()=>D),[D]),t.createElement(Qn.Provider,{value:C},t.createElement(Ee,{context:qn,store:I},e.children))}let ko=0;var Wo="useId"in t?function(){return t.useId()}:function(){return Pe((()=>""+ko++),[])};const Uo={dragging:5e3,dropAnimating:4500},Ho=(e,t)=>t?_r.drop(t.duration):e?_r.snap:_r.fluid,qo=(e,t)=>{if(e)return t?Tr.drop:Tr.combining},Vo=e=>null!=e.forceShouldAnimate?e.forceShouldAnimate:"SNAP"===e.mode;function zo(e){return"DRAGGING"===e.type?function(e){const t=e.dimension.client,{offset:r,combineWith:n,dropping:o}=e,i=Boolean(n),a=Vo(e),s=Boolean(o),l=s?Fr.drop(r,i):Fr.moveTo(r);return{position:"fixed",top:t.marginBox.top,left:t.marginBox.left,boxSizing:"border-box",width:t.borderBox.width,height:t.borderBox.height,transition:Ho(a,o),transform:l,opacity:qo(i,s),zIndex:s?Uo.dropAnimating:Uo.dragging,pointerEvents:"none"}}(e):(t=e,{transform:Fr.moveTo(t.offset),transition:t.shouldAnimateDisplacement?void 0:"none"});var t}function Yo(e){const r=Kn("draggable"),{descriptor:n,registry:o,getDraggableRef:i,canDragInteractiveElements:a,shouldRespectForcePress:s,isEnabled:l}=e,c=Pe((()=>({canDragInteractiveElements:a,shouldRespectForcePress:s,isEnabled:l})),[a,l,s]),d=Re((e=>{const t=i();return t||u(!1),function(e,t,r=Ne){const n=window.getComputedStyle(t),o=t.getBoundingClientRect(),i=Ye(o,n),a=ze(i,r);return{descriptor:e,placeholder:{client:i,tagName:t.tagName.toLowerCase(),display:n.display},displaceBy:{x:i.marginBox.width,y:i.marginBox.height},client:i,page:a}}(n,t,e)}),[n,i]),p=Pe((()=>({uniqueId:r,descriptor:n,options:c,getDimension:d})),[n,d,c,r]),f=t.useRef(p),g=t.useRef(!0);_n((()=>(o.draggable.register(f.current),()=>o.draggable.unregister(f.current))),[o.draggable]),_n((()=>{if(g.current)return void(g.current=!1);const e=f.current;f.current=p,o.draggable.update(p,e)}),[p,o.draggable])}var Jo=t.createContext(null);function Xo(e){const r=t.useContext(e);return r||u(!1),r}function Ko(e){e.preventDefault()}var Qo=e=>{const n=t.useRef(null),o=Re(((e=null)=>{n.current=e}),[]),i=Re((()=>n.current),[]),{contextId:a,dragHandleUsageInstructionsId:s,registry:l}=Xo(Qn),{type:c,droppableId:d}=Xo(Jo),u=Pe((()=>({id:e.draggableId,index:e.index,type:c,droppableId:d})),[e.draggableId,e.index,c,d]),{children:p,draggableId:f,isEnabled:g,shouldRespectForcePress:m,canDragInteractiveElements:b,isClone:h,mapped:y,dropAnimationFinished:v}=e;if(!h){Yo(Pe((()=>({descriptor:u,registry:l,getDraggableRef:i,canDragInteractiveElements:b,shouldRespectForcePress:m,isEnabled:g})),[u,l,i,b,m,g]))}const x=Pe((()=>g?{tabIndex:0,role:"button","aria-describedby":s,"data-rfd-drag-handle-draggable-id":f,"data-rfd-drag-handle-context-id":a,draggable:!1,onDragStart:Ko}:null),[a,s,f,g]),I=Re((e=>{"DRAGGING"===y.type&&y.dropping&&"transform"===e.propertyName&&(t.version.startsWith("16")||t.version.startsWith("17")?v():r.flushSync(v))}),[v,y]),D=Pe((()=>{const e=zo(y),t="DRAGGING"===y.type&&y.dropping?I:void 0;return{innerRef:o,draggableProps:{"data-rfd-draggable-context-id":a,"data-rfd-draggable-id":f,style:e,onTransitionEnd:t},dragHandleProps:x}}),[a,x,f,y,I,o]),w=Pe((()=>({draggableId:u.id,type:u.type,source:{index:u.index,droppableId:u.droppableId}})),[u.droppableId,u.id,u.index,u.type]);return t.createElement(t.Fragment,null,p(D,y.snapshot,w))},Zo=(e,t)=>e===t,ei=e=>{const{combine:t,destination:r}=e;return r?r.droppableId:t?t.droppableId:null};function ti(e=null){return{isDragging:!1,isDropAnimating:!1,isClone:!1,dropAnimation:null,mode:null,draggingOver:null,combineTargetFor:e,combineWith:null}}const ri={mapped:{type:"SECONDARY",offset:Ne,combineTargetFor:null,shouldAnimateDisplacement:!0,snapshot:ti(null)}};const ni=Se((()=>{const e=function(){const e=nt(((e,t)=>({x:e,y:t}))),t=nt(((e,t,r=null,n=null,o=null)=>({isDragging:!0,isClone:t,isDropAnimating:Boolean(o),dropAnimation:o,mode:e,draggingOver:r,combineWith:n,combineTargetFor:null}))),r=nt(((e,r,n,o,i=null,a=null,s=null)=>({mapped:{type:"DRAGGING",dropping:null,draggingOver:i,combineWith:a,mode:r,offset:e,dimension:n,forceShouldAnimate:s,snapshot:t(r,o,i,a,null)}})));return(n,o)=>{if(eo(n)){if(n.critical.draggable.id!==o.draggableId)return null;const t=n.current.client.offset,a=n.dimensions.draggables[o.draggableId],s=Xt(n.impact),l=(i=n.impact).at&&"COMBINE"===i.at.type?i.at.combine.draggableId:null,c=n.forceShouldAnimate;return r(e(t.x,t.y),n.movementMode,a,o.isClone,s,l,c)}var i;if("DROP_ANIMATING"===n.phase){const e=n.completed;if(e.result.draggableId!==o.draggableId)return null;const r=o.isClone,i=n.dimensions.draggables[o.draggableId],a=e.result,s=a.mode,l=ei(a),c=(e=>e.combine?e.combine.draggableId:null)(a),d={duration:n.dropDuration,curve:Br,moveTo:n.newHomeClientOffset,opacity:c?Tr.drop:null,scale:c?Lr.drop:null};return{mapped:{type:"DRAGGING",offset:n.newHomeClientOffset,dimension:i,dropping:d,draggingOver:l,combineWith:c,mode:s,forceShouldAnimate:null,snapshot:t(s,r,l,c,d)}}}return null}}(),t=function(){const e=nt(((e,t)=>({x:e,y:t}))),t=nt(ti),r=nt(((e,r=null,n)=>({mapped:{type:"SECONDARY",offset:e,combineTargetFor:r,shouldAnimateDisplacement:n,snapshot:t(r)}}))),n=e=>e?r(Ne,e,!0):null,o=(t,o,i,a)=>{const s=i.displaced.visible[t],l=Boolean(a.inVirtualList&&a.effected[t]),c=dt(i),d=c&&c.draggableId===t?o:null;if(!s){if(!l)return n(d);if(i.displaced.invisible[t])return null;const o=Me(a.displacedBy.point),s=e(o.x,o.y);return r(s,d,!0)}if(l)return n(d);const u=i.displacedBy.point,p=e(u.x,u.y);return r(p,d,s.shouldAnimate)};return(e,t)=>{if(eo(e))return e.critical.draggable.id===t.draggableId?null:o(t.draggableId,e.critical.draggable.id,e.impact,e.afterCritical);if("DROP_ANIMATING"===e.phase){const r=e.completed;return r.result.draggableId===t.draggableId?null:o(t.draggableId,r.result.draggableId,r.impact,r.afterCritical)}return null}}();return(r,n)=>e(r,n)||t(r,n)||ri}),{dropAnimationFinished:Nr},null,{context:qn,areStatePropsEqual:Zo})(Qo);var oi=ni;function ii(e){return Xo(Jo).isUsingCloneFor!==e.draggableId||e.isClone?t.createElement(oi,e):null}const ai=e=>t=>e===t,si=ai("scroll"),li=ai("auto"),ci=(e,t)=>t(e.overflowX)||t(e.overflowY),di=e=>null==e||e===document.body||e===document.documentElement?null:(e=>{const t=window.getComputedStyle(e),r={overflowX:t.overflowX,overflowY:t.overflowY};return ci(r,si)||ci(r,li)})(e)?e:di(e.parentElement);var ui=e=>({x:e.scrollLeft,y:e.scrollTop});const pi=e=>{if(!e)return!1;return"fixed"===window.getComputedStyle(e).position||pi(e.parentElement)};var fi=({ref:e,descriptor:t,env:r,windowScroll:n,direction:o,isDropDisabled:i,isCombineEnabled:a,shouldClipSubject:s})=>{const l=r.closestScrollable,c=((e,t)=>{const r=Je(e);if(!t)return r;if(e!==t)return r;const n=r.paddingBox.top-t.scrollTop,o=r.paddingBox.left-t.scrollLeft,i=n+t.scrollHeight,a=o+t.scrollWidth,s=ke({top:n,right:a,bottom:i,left:o},r.border);return He({borderBox:s,margin:r.margin,border:r.border,padding:r.padding})})(e,l),d=ze(c,n),u=(()=>{if(!l)return null;const e=Je(l),t={scrollHeight:l.scrollHeight,scrollWidth:l.scrollWidth};return{client:e,page:ze(e,n),scroll:ui(l),scrollSize:t,shouldClipSubject:s}})(),p=(({descriptor:e,isEnabled:t,isCombineEnabled:r,isFixedOnPage:n,direction:o,client:i,page:a,closest:s})=>{const l=(()=>{if(!s)return null;const{scrollSize:e,client:t}=s,r=on({scrollHeight:e.scrollHeight,scrollWidth:e.scrollWidth,height:t.paddingBox.height,width:t.paddingBox.width});return{pageMarginBox:s.page.marginBox,frameClient:t,scrollSize:e,shouldClipSubject:s.shouldClipSubject,scroll:{initial:s.scroll,current:s.scroll,max:r,diff:{value:Ne,displacement:Ne}}}})(),c="vertical"===o?vt:xt;return{descriptor:e,isCombineEnabled:r,isFixedOnPage:n,axis:c,isEnabled:t,client:i,page:a,frame:l,subject:Ze({page:a,withPlaceholder:null,axis:c,frame:l})}})({descriptor:t,isEnabled:!i,isCombineEnabled:a,isFixedOnPage:r.isFixedOnPage,direction:o,client:c,page:d,closest:u});return p};const gi={passive:!1},mi={passive:!0};var bi=e=>e.shouldPublishImmediately?gi:mi;const hi=e=>e&&e.env.closestScrollable||null;function yi(e){const r=t.useRef(null),n=Xo(Qn),o=Kn("droppable"),{registry:i,marshal:a}=n,s=Zn(e),l=Pe((()=>({id:e.droppableId,type:e.type,mode:e.mode})),[e.droppableId,e.mode,e.type]),c=t.useRef(l),d=Pe((()=>nt(((e,t)=>{r.current||u(!1);const n={x:e,y:t};a.updateDroppableScroll(l.id,n)}))),[l.id,a]),p=Re((()=>{const e=r.current;return e&&e.env.closestScrollable?ui(e.env.closestScrollable):Ne}),[]),f=Re((()=>{const e=p();d(e.x,e.y)}),[p,d]),g=Pe((()=>Hr(f)),[f]),m=Re((()=>{const e=r.current,t=hi(e);e&&t||u(!1);e.scrollOptions.shouldPublishImmediately?f():g()}),[g,f]),b=Re(((e,t)=>{r.current&&u(!1);const o=s.current,i=o.getDroppableRef();i||u(!1);const a={closestScrollable:di(c=i),isFixedOnPage:pi(c)};var c;const d={ref:i,descriptor:l,env:a,scrollOptions:t};r.current=d;const p=fi({ref:i,descriptor:l,env:a,windowScroll:e,direction:o.direction,isDropDisabled:o.isDropDisabled,isCombineEnabled:o.isCombineEnabled,shouldClipSubject:!o.ignoreContainerClipping}),f=a.closestScrollable;return f&&(f.setAttribute(Ln.contextId,n.contextId),f.addEventListener("scroll",m,bi(d.scrollOptions))),p}),[n.contextId,l,m,s]),h=Re((()=>{const e=r.current,t=hi(e);return e&&t||u(!1),ui(t)}),[]),y=Re((()=>{const e=r.current;e||u(!1);const t=hi(e);r.current=null,t&&(g.cancel(),t.removeAttribute(Ln.contextId),t.removeEventListener("scroll",m,bi(e.scrollOptions)))}),[m,g]),v=Re((e=>{const t=r.current;t||u(!1);const n=hi(t);n||u(!1),n.scrollTop+=e.y,n.scrollLeft+=e.x}),[]),x=Pe((()=>({getDimensionAndWatchScroll:b,getScrollWhileDragging:h,dragStopped:y,scroll:v})),[y,b,h,v]),I=Pe((()=>({uniqueId:o,descriptor:l,callbacks:x})),[x,l,o]);_n((()=>(c.current=I.descriptor,i.droppable.register(I),()=>{r.current&&y(),i.droppable.unregister(I)})),[x,l,y,I,a,i.droppable]),_n((()=>{r.current&&a.updateDroppableIsEnabled(c.current.id,!e.isDropDisabled)}),[e.isDropDisabled,a]),_n((()=>{r.current&&a.updateDroppableIsCombineEnabled(c.current.id,e.isCombineEnabled)}),[e.isCombineEnabled,a])}function vi(){}const xi={width:0,height:0,margin:{top:0,right:0,bottom:0,left:0}},Ii=({isAnimatingOpenOnMount:e,placeholder:t,animate:r})=>{const n=(({isAnimatingOpenOnMount:e,placeholder:t,animate:r})=>e||"close"===r?xi:{height:t.client.borderBox.height,width:t.client.borderBox.width,margin:t.client.margin})({isAnimatingOpenOnMount:e,placeholder:t,animate:r});return{display:t.display,boxSizing:"border-box",width:n.width,height:n.height,marginTop:n.margin.top,marginRight:n.margin.right,marginBottom:n.margin.bottom,marginLeft:n.margin.left,flexShrink:"0",flexGrow:"0",pointerEvents:"none",transition:"none"!==r?_r.placeholder:null}};var Di=t.memo((e=>{const r=t.useRef(null),n=Re((()=>{r.current&&(clearTimeout(r.current),r.current=null)}),[]),{animate:o,onTransitionEnd:i,onClose:a,contextId:s}=e,[l,c]=t.useState("open"===e.animate);t.useEffect((()=>l?"open"!==o?(n(),c(!1),vi):r.current?vi:(r.current=setTimeout((()=>{r.current=null,c(!1)})),n):vi),[o,l,n]);const d=Re((e=>{"height"===e.propertyName&&(i(),"close"===o&&a())}),[o,a,i]),u=Ii({isAnimatingOpenOnMount:l,animate:e.animate,placeholder:e.placeholder});return t.createElement(e.placeholder.tagName,{style:u,"data-rfd-placeholder-context-id":s,onTransitionEnd:d,ref:e.innerRef})}));class wi extends t.PureComponent{constructor(...e){super(...e),this.state={isVisible:Boolean(this.props.on),data:this.props.on,animate:this.props.shouldAnimate&&this.props.on?"open":"none"},this.onClose=()=>{"close"===this.state.animate&&this.setState({isVisible:!1})}}static getDerivedStateFromProps(e,t){return e.shouldAnimate?e.on?{isVisible:!0,data:e.on,animate:"open"}:t.isVisible?{isVisible:!0,data:t.data,animate:"close"}:{isVisible:!1,animate:"close",data:null}:{isVisible:Boolean(e.on),data:e.on,animate:"none"}}render(){if(!this.state.isVisible)return null;const e={onClose:this.onClose,data:this.state.data,animate:this.state.animate};return this.props.children(e)}}var Si=e=>{const n=t.useContext(Qn);n||u(!1);const{contextId:o,isMovementAllowed:i}=n,a=t.useRef(null),s=t.useRef(null),{children:l,droppableId:c,type:d,mode:p,direction:f,ignoreContainerClipping:g,isDropDisabled:m,isCombineEnabled:b,snapshot:h,useClone:y,updateViewportMaxScroll:v,getContainerForClone:x}=e,I=Re((()=>a.current),[]),D=Re(((e=null)=>{a.current=e}),[]);Re((()=>s.current),[]);const w=Re(((e=null)=>{s.current=e}),[]),S=Re((()=>{i()&&v({maxScroll:sn()})}),[i,v]);yi({droppableId:c,type:d,mode:p,direction:f,isDropDisabled:m,isCombineEnabled:b,ignoreContainerClipping:g,getDroppableRef:I});const E=Pe((()=>t.createElement(wi,{on:e.placeholder,shouldAnimate:e.shouldAnimatePlaceholder},(({onClose:e,data:r,animate:n})=>t.createElement(Di,{placeholder:r,onClose:e,innerRef:w,animate:n,contextId:o,onTransitionEnd:S})))),[o,S,e.placeholder,e.shouldAnimatePlaceholder,w]),C=Pe((()=>({innerRef:D,placeholder:E,droppableProps:{"data-rfd-droppable-id":c,"data-rfd-droppable-context-id":o}})),[o,c,E,D]),O=y?y.dragging.draggableId:null,A=Pe((()=>({droppableId:c,type:d,isUsingCloneFor:O})),[c,O,d]);return t.createElement(Jo.Provider,{value:A},l(C,h),function(){if(!y)return null;const{dragging:e,render:n}=y,o=t.createElement(ii,{draggableId:e.draggableId,index:e.source.index,isClone:!0,isEnabled:!0,shouldRespectForcePress:!1,canDragInteractiveElements:!0},((t,r)=>n(t,r,e)));return r.createPortal(o,x())}())};const Ei={mode:"standard",type:"DEFAULT",direction:"vertical",isDropDisabled:!1,isCombineEnabled:!1,ignoreContainerClipping:!1,renderClone:null,getContainerForClone:function(){return document.body||u(!1),document.body}},Ci=e=>{let t,r={...e};for(t in Ei)void 0===e[t]&&(r={...r,[t]:Ei[t]});return r},Oi=(e,t)=>e===t.droppable.type,Ai=(e,t)=>t.draggables[e.draggable.id];var Pi=Se((()=>{const e={placeholder:null,shouldAnimatePlaceholder:!0,snapshot:{isDraggingOver:!1,draggingOverWith:null,draggingFromThisWith:null,isUsingPlaceholder:!1},useClone:null},t={...e,shouldAnimatePlaceholder:!1},r=nt((e=>({draggableId:e.id,type:e.type,source:{index:e.index,droppableId:e.droppableId}}))),n=nt(((n,o,i,a,s,l)=>{const c=s.descriptor.id;if(s.descriptor.droppableId===n){const e=l?{render:l,dragging:r(s.descriptor)}:null,t={isDraggingOver:i,draggingOverWith:i?c:null,draggingFromThisWith:c,isUsingPlaceholder:!0};return{placeholder:s.placeholder,shouldAnimatePlaceholder:!1,snapshot:t,useClone:e}}if(!o)return t;if(!a)return e;const d={isDraggingOver:i,draggingOverWith:c,draggingFromThisWith:null,isUsingPlaceholder:!0};return{placeholder:s.placeholder,shouldAnimatePlaceholder:!0,snapshot:d,useClone:null}}));return(r,o)=>{const i=Ci(o),a=i.droppableId,s=i.type,l=!i.isDropDisabled,c=i.renderClone;if(eo(r)){const e=r.critical;if(!Oi(s,e))return t;const o=Ai(e,r.dimensions),i=Xt(r.impact)===a;return n(a,l,i,i,o,c)}if("DROP_ANIMATING"===r.phase){const e=r.completed;if(!Oi(s,e.critical))return t;const o=Ai(e.critical,r.dimensions);return n(a,l,ei(e.result)===a,Xt(e.impact)===a,o,c)}if("IDLE"===r.phase&&r.completed&&!r.shouldFlush){const n=r.completed;if(!Oi(s,n.critical))return t;const o=Xt(n.impact)===a,i=Boolean(n.impact.at&&"COMBINE"===n.impact.at.type),l=n.critical.droppable.id===a;return o?i?e:t:l?e:t}return t}}),{updateViewportMaxScroll:e=>({type:"UPDATE_VIEWPORT_MAX_SCROLL",payload:e})},((e,t,r)=>({...Ci(r),...e,...t})),{context:qn,areStatePropsEqual:Zo})(Si);e.DragDropContext=function(e){const r=Wo(),n=e.dragHandleUsageInstructions||y.dragHandleUsageInstructions;return t.createElement(p,null,(o=>t.createElement(jo,{nonce:e.nonce,contextId:r,setCallbacks:o,dragHandleUsageInstructions:n,enableDefaultSensors:e.enableDefaultSensors,sensors:e.sensors,onBeforeCapture:e.onBeforeCapture,onBeforeDragStart:e.onBeforeDragStart,onDragStart:e.onDragStart,onDragUpdate:e.onDragUpdate,onDragEnd:e.onDragEnd,autoScrollerOptions:e.autoScrollerOptions},e.children)))},e.Draggable=function(e){const r="boolean"!=typeof e.isDragDisabled||!e.isDragDisabled,n=Boolean(e.disableInteractiveElementBlocking),o=Boolean(e.shouldRespectForcePress);return t.createElement(ii,_({},e,{isClone:!1,isEnabled:r,canDragInteractiveElements:n,shouldRespectForcePress:o}))},e.Droppable=Pi,e.resetServerContext=function(){"useId"in t||(ko=0,Jn=0)},e.useKeyboardSensor=vo,e.useMouseSensor=mo,e.useTouchSensor=Do}));
