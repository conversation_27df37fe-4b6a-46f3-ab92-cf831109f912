'use client';

import { useState, useEffect } from 'react';
import Link from 'next/link';
import { <PERSON><PERSON>, X, ExternalLink } from 'lucide-react';

const CookieConsent = () => {
  const [showBanner, setShowBanner] = useState(false);

  useEffect(() => {
    // Check if user has already made a choice
    const cookieConsent = localStorage.getItem('cookieConsent');
    if (!cookieConsent) {
      // Show banner after a short delay to not interfere with page load
      const timer = setTimeout(() => {
        setShowBanner(true);
      }, 2000);
      
      return () => clearTimeout(timer);
    }
  }, []);

  const handleAccept = () => {
    const consent = {
      essential: true,
      analytics: true,
      marketing: false, // Conservative default for internal system
      functional: true,
      timestamp: new Date().toISOString()
    };
    
    localStorage.setItem('cookieConsent', JSON.stringify(consent));
    setShowBanner(false);
    
    // Initialize analytics for internal system
    if (typeof window !== 'undefined' && window.gtag) {
      window.gtag('consent', 'update', {
        analytics_storage: 'granted'
      });
    }
  };

  const handleDecline = () => {
    const consent = {
      essential: true,
      analytics: false,
      marketing: false,
      functional: false,
      timestamp: new Date().toISOString()
    };
    
    localStorage.setItem('cookieConsent', JSON.stringify(consent));
    setShowBanner(false);
  };

  if (!showBanner) return null;

  return (
    <div className="fixed bottom-4 right-4 max-w-sm bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-lg shadow-lg z-50 p-4">
      <div className="flex items-start justify-between mb-3">
        <div className="flex items-center space-x-2">
          <Cookie className="text-primary-600 flex-shrink-0" size={20} />
          <h3 className="text-sm font-semibold text-gray-900 dark:text-white">
            Cookies
          </h3>
        </div>
        <button
          onClick={() => setShowBanner(false)}
          className="text-gray-400 hover:text-gray-600 dark:hover:text-gray-300"
        >
          <X size={16} />
        </button>
      </div>
      
      <p className="text-xs text-gray-600 dark:text-gray-400 mb-4">
        Utilizamos cookies para melhorar sua experiência no sistema. 
        Consulte nossa{' '}
        <Link 
          href="/legal/politica-cookies" 
          target="_blank"
          className="text-primary-600 hover:text-primary-700 underline inline-flex items-center"
        >
          Política de Cookies
          <ExternalLink size={12} className="ml-1" />
        </Link>
        .
      </p>
      
      <div className="flex space-x-2">
        <button
          onClick={handleDecline}
          className="flex-1 px-3 py-2 text-xs border border-gray-300 dark:border-gray-600 text-gray-700 dark:text-gray-300 rounded hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors"
        >
          Recusar
        </button>
        <button
          onClick={handleAccept}
          className="flex-1 px-3 py-2 text-xs bg-primary-600 text-white rounded hover:bg-primary-700 transition-colors"
        >
          Aceitar
        </button>
      </div>
    </div>
  );
};

export default CookieConsent;
