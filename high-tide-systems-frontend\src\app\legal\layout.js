'use client';

import Link from 'next/link';
import { <PERSON>L<PERSON>t, Shield, FileText, <PERSON><PERSON>, <PERSON> } from 'lucide-react';

export default function LegalLayout({ children }) {
  return (
    <div className="min-h-screen bg-gray-50 dark:bg-gray-900">
      {/* Header */}
      <header className="bg-white dark:bg-gray-800 shadow-sm border-b border-gray-200 dark:border-gray-700">
        <div className="container mx-auto px-4 py-4">
          <div className="flex items-center justify-between">
            <Link href="/landing" className="flex items-center space-x-2 text-primary-600 hover:text-primary-700 transition-colors">
              <ArrowLeft size={20} />
              <span>Voltar ao site</span>
            </Link>
            <Link href="/landing" className="flex items-center space-x-2">
              <img
                src="/logo_horizontal_sem_fundo.png"
                alt="High Tide Systems"
                className="h-8 dark:invert"
              />
            </Link>
          </div>
        </div>
      </header>

      <div className="container mx-auto px-4 py-8">
        <div className="flex flex-col lg:flex-row gap-8">
          {/* Sidebar */}
          <aside className="lg:w-64 flex-shrink-0">
            <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm p-6">
              <h2 className="text-lg font-semibold text-gray-900 dark:text-white mb-4">
                Documentos Legais
              </h2>
              <nav className="space-y-2">
                <Link
                  href="/legal/politica-privacidade"
                  className="flex items-center space-x-3 p-3 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors"
                >
                  <Shield size={18} className="text-primary-600" />
                  <span className="text-gray-700 dark:text-gray-300">Política de Privacidade</span>
                </Link>
                <Link
                  href="/legal/termos-uso"
                  className="flex items-center space-x-3 p-3 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors"
                >
                  <FileText size={18} className="text-primary-600" />
                  <span className="text-gray-700 dark:text-gray-300">Termos de Uso</span>
                </Link>
                <Link
                  href="/legal/politica-cookies"
                  className="flex items-center space-x-3 p-3 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors"
                >
                  <Cookie size={18} className="text-primary-600" />
                  <span className="text-gray-700 dark:text-gray-300">Política de Cookies</span>
                </Link>
                <Link
                  href="/legal/politica-seguranca"
                  className="flex items-center space-x-3 p-3 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors"
                >
                  <Lock size={18} className="text-primary-600" />
                  <span className="text-gray-700 dark:text-gray-300">Política de Segurança</span>
                </Link>
              </nav>

              <div className="mt-8 p-4 bg-primary-50 dark:bg-primary-900/20 rounded-lg">
                <h3 className="text-sm font-semibold text-primary-900 dark:text-primary-100 mb-2">
                  Exercer Direitos LGPD
                </h3>
                <p className="text-xs text-primary-700 dark:text-primary-300 mb-3">
                  Solicite acesso, correção ou exclusão dos seus dados pessoais.
                </p>
                <Link
                  href="/legal/exercer-direitos"
                  className="inline-flex items-center justify-center w-full px-3 py-2 text-xs font-medium text-white bg-primary-600 rounded-md hover:bg-primary-700 transition-colors"
                >
                  Exercer Direitos
                </Link>
              </div>
            </div>
          </aside>

          {/* Main Content */}
          <main className="flex-1">
            <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm">
              {children}
            </div>
          </main>
        </div>
      </div>

      {/* Footer */}
      <footer className="bg-white dark:bg-gray-800 border-t border-gray-200 dark:border-gray-700 mt-16">
        <div className="container mx-auto px-4 py-6">
          <div className="text-center text-sm text-gray-600 dark:text-gray-400">
            <p>&copy; 2025 High Tide Systems. Todos os direitos reservados.</p>
            <p className="mt-1">
              Em conformidade com a Lei Geral de Proteção de Dados (LGPD - Lei nº 13.709/2018)
            </p>
          </div>
        </div>
      </footer>
    </div>
  );
}
