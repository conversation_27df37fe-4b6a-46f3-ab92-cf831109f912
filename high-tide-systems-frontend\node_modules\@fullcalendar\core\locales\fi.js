var l28 = {
    code: 'fi',
    week: {
        dow: 1,
        doy: 4, // The week that contains Jan 4th is the first week of the year.
    },
    buttonText: {
        prev: '<PERSON><PERSON><PERSON>',
        next: '<PERSON><PERSON><PERSON>',
        today: '<PERSON>ä<PERSON><PERSON><PERSON><PERSON>',
        year: 'Vuosi',
        month: '<PERSON><PERSON><PERSON><PERSON>',
        week: '<PERSON>iik<PERSON>',
        day: '<PERSON><PERSON><PERSON><PERSON>',
        list: '<PERSON><PERSON>htuma<PERSON>',
    },
    weekText: 'Vk',
    allDayText: 'Koko päivä',
    moreLinkText: 'lisää',
    noEventsText: 'Ei näytettäviä tapahtumia',
};

export { l28 as default };
