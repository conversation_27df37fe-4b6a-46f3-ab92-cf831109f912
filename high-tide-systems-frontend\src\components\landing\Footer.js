'use client';

import Link from 'next/link';
import { Facebook, Twitter, Instagram, Linkedin, Mail, Phone, MapPin } from 'lucide-react';
import { scrollToElement } from '@/utils/scrollUtils';

const Footer = () => {
  const currentYear = new Date().getFullYear();

  return (
    <footer className="bg-gray-50 dark:bg-gray-800 pt-16 pb-8">
      <div className="container mx-auto px-4 md:px-6">
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-8 mb-12">
          {/* Logo and description */}
          <div className="lg:col-span-2">
            <Link href="/" className="inline-block mb-4">
              <img
                src="/logo_horizontal_sem_fundo.png"
                alt="High Tide Logo"
                className="h-10 dark:invert"
              />
            </Link>
            <p className="text-gray-600 dark:text-gray-300 mb-4 max-w-md">
              High Tide Systems é a solução completa para seu negócio! Gerencie clientes, controle agendamentos, administre usuários, mantenha comunicação integrada e personalize conforme suas necessidades.
            </p>
            <div className="flex space-x-4">
              <a href="#" className="text-gray-400 hover:text-primary-500 dark:hover:text-primary-400 transition-colors">
                <Facebook size={20} />
              </a>
              <a href="#" className="text-gray-400 hover:text-primary-500 dark:hover:text-primary-400 transition-colors">
                <Twitter size={20} />
              </a>
              <a href="https://instagram.com/hightidesystems" target="_blank" rel="noopener noreferrer" className="text-gray-400 hover:text-primary-500 dark:hover:text-primary-400 transition-colors">
                <Instagram size={20} />
              </a>
              <a href="#" className="text-gray-400 hover:text-primary-500 dark:hover:text-primary-400 transition-colors">
                <Linkedin size={20} />
              </a>
            </div>
          </div>

          {/* Links */}
          <div>
            <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-4">Produto</h3>
            <ul className="space-y-3">
              <li>
                <a
                  href="#features"
                  onClick={(e) => {
                    e.preventDefault();
                    scrollToElement('features', 80);
                  }}
                  className="text-gray-600 dark:text-gray-300 hover:text-primary-500 dark:hover:text-primary-400 transition-colors cursor-pointer"
                >
                  Recursos
                </a>
              </li>
              <li>
                <a
                  href="#modules"
                  onClick={(e) => {
                    e.preventDefault();
                    scrollToElement('modules', 80);
                  }}
                  className="text-gray-600 dark:text-gray-300 hover:text-primary-500 dark:hover:text-primary-400 transition-colors cursor-pointer"
                >
                  Módulos
                </a>
              </li>
              <li>
                <a
                  href="#pricing"
                  onClick={(e) => {
                    e.preventDefault();
                    scrollToElement('pricing', 80);
                  }}
                  className="text-gray-600 dark:text-gray-300 hover:text-primary-500 dark:hover:text-primary-400 transition-colors cursor-pointer"
                >
                  Preços
                </a>
              </li>
              <li>
                <Link href="#" className="text-gray-600 dark:text-gray-300 hover:text-primary-500 dark:hover:text-primary-400 transition-colors">
                  Atualizações
                </Link>
              </li>
            </ul>
          </div>

          <div>
            <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-4">Empresa</h3>
            <ul className="space-y-3">
              <li>
                <a
                  href="#about"
                  onClick={(e) => {
                    e.preventDefault();
                    scrollToElement('about', 80);
                  }}
                  className="text-gray-600 dark:text-gray-300 hover:text-primary-500 dark:hover:text-primary-400 transition-colors cursor-pointer"
                >
                  Sobre nós
                </a>
              </li>
              <li>
                <Link href="#" className="text-gray-600 dark:text-gray-300 hover:text-primary-500 dark:hover:text-primary-400 transition-colors">
                  Clientes
                </Link>
              </li>
              <li>
                <Link href="#" className="text-gray-600 dark:text-gray-300 hover:text-primary-500 dark:hover:text-primary-400 transition-colors">
                  Carreiras
                </Link>
              </li>
              <li>
                <Link href="#" className="text-gray-600 dark:text-gray-300 hover:text-primary-500 dark:hover:text-primary-400 transition-colors">
                  Blog
                </Link>
              </li>
            </ul>
          </div>

          <div>
            <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-4">Contato</h3>
            <ul className="space-y-3">
              <li className="flex items-start">
                <Mail className="mr-2 h-5 w-5 text-gray-400" />
                <a href="mailto:<EMAIL>" className="text-gray-600 dark:text-gray-300 hover:text-primary-500 dark:hover:text-primary-400 transition-colors">
                  <EMAIL>
                </a>
              </li>
              <li className="flex items-start">
                <Phone className="mr-2 h-5 w-5 text-gray-400" />
                <a href="tel:+5511999999999" className="text-gray-600 dark:text-gray-300 hover:text-primary-500 dark:hover:text-primary-400 transition-colors">
                  +55 (11) 99999-9999
                </a>
              </li>
              <li className="flex items-start">
                <MapPin className="mr-2 h-5 w-5 text-gray-400" />
                <span className="text-gray-600 dark:text-gray-300">
                  São Paulo, SP - Brasil
                </span>
              </li>
            </ul>
          </div>
        </div>

        <div className="border-t border-gray-200 dark:border-gray-700 pt-8">
          <div className="flex flex-col md:flex-row justify-between items-center">
            <div className="text-center md:text-left mb-4 md:mb-0">
              <p className="text-gray-500 dark:text-gray-400 text-sm">
                &copy; {currentYear} High Tide Systems. Todos os direitos reservados.
              </p>
              <p className="text-gray-400 dark:text-gray-500 text-xs mt-1">
                Em conformidade com a LGPD (Lei nº 13.709/2018)
              </p>
            </div>
            <div className="flex flex-wrap justify-center md:justify-end gap-4 md:gap-6">
              <Link href="/legal/termos-uso" className="text-gray-500 dark:text-gray-400 text-sm hover:text-primary-500 dark:hover:text-primary-400 transition-colors">
                Termos de Uso
              </Link>
              <Link href="/legal/politica-privacidade" className="text-gray-500 dark:text-gray-400 text-sm hover:text-primary-500 dark:hover:text-primary-400 transition-colors">
                Política de Privacidade
              </Link>
              <Link href="/legal/politica-cookies" className="text-gray-500 dark:text-gray-400 text-sm hover:text-primary-500 dark:hover:text-primary-400 transition-colors">
                Cookies
              </Link>
              <Link href="/legal/politica-seguranca" className="text-gray-500 dark:text-gray-400 text-sm hover:text-primary-500 dark:hover:text-primary-400 transition-colors">
                Segurança
              </Link>
              <Link href="/legal/exercer-direitos" className="text-gray-500 dark:text-gray-400 text-sm hover:text-primary-500 dark:hover:text-primary-400 transition-colors">
                Exercer Direitos LGPD
              </Link>
            </div>
          </div>
        </div>
      </div>
    </footer>
  );
};

export default Footer;
