"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/landing/page",{

/***/ "(app-pages-browser)/./src/app/landing/page.js":
/*!*********************************!*\
  !*** ./src/app/landing/page.js ***!
  \*********************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ LandingPage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var _components_landing_Header__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/components/landing/Header */ \"(app-pages-browser)/./src/components/landing/Header.js\");\n/* harmony import */ var _components_landing_Hero__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/landing/Hero */ \"(app-pages-browser)/./src/components/landing/Hero.js\");\n/* harmony import */ var _components_landing_Features__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/landing/Features */ \"(app-pages-browser)/./src/components/landing/Features.js\");\n/* harmony import */ var _components_landing_About__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/landing/About */ \"(app-pages-browser)/./src/components/landing/About.js\");\n/* harmony import */ var _components_landing_FutureImplementations__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/landing/FutureImplementations */ \"(app-pages-browser)/./src/components/landing/FutureImplementations.js\");\n/* harmony import */ var _components_landing_Modules__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/landing/Modules */ \"(app-pages-browser)/./src/components/landing/Modules.js\");\n/* harmony import */ var _components_landing_Stats__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/landing/Stats */ \"(app-pages-browser)/./src/components/landing/Stats.js\");\n/* harmony import */ var _components_landing_Pricing__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/components/landing/Pricing */ \"(app-pages-browser)/./src/components/landing/Pricing.js\");\n/* harmony import */ var _components_landing_FAQ__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/components/landing/FAQ */ \"(app-pages-browser)/./src/components/landing/FAQ.js\");\n/* harmony import */ var _components_landing_CTA__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @/components/landing/CTA */ \"(app-pages-browser)/./src/components/landing/CTA.js\");\n/* harmony import */ var _components_landing_Footer__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @/components/landing/Footer */ \"(app-pages-browser)/./src/components/landing/Footer.js\");\n/* harmony import */ var _components_landing_CookieBanner__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! @/components/landing/CookieBanner */ \"(app-pages-browser)/./src/components/landing/CookieBanner.js\");\n/* harmony import */ var _styles_landing_animations_css__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! @/styles/landing-animations.css */ \"(app-pages-browser)/./src/styles/landing-animations.css\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n\n\n\n\n\n\n\n\n\n\nfunction LandingPage() {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_landing_Header__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {}, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\landing\\\\page.js\",\n                lineNumber: 20,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_landing_Hero__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {}, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\landing\\\\page.js\",\n                        lineNumber: 22,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_landing_Pricing__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {}, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\landing\\\\page.js\",\n                        lineNumber: 23,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_landing_Features__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {}, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\landing\\\\page.js\",\n                        lineNumber: 24,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_landing_Stats__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {}, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\landing\\\\page.js\",\n                        lineNumber: 25,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_landing_Modules__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {}, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\landing\\\\page.js\",\n                        lineNumber: 26,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_landing_FutureImplementations__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {}, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\landing\\\\page.js\",\n                        lineNumber: 27,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_landing_About__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {}, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\landing\\\\page.js\",\n                        lineNumber: 28,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_landing_FAQ__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {}, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\landing\\\\page.js\",\n                        lineNumber: 29,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_landing_CTA__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {}, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\landing\\\\page.js\",\n                        lineNumber: 30,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\landing\\\\page.js\",\n                lineNumber: 21,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_landing_Footer__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {}, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\landing\\\\page.js\",\n                lineNumber: 32,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true);\n}\n_c = LandingPage;\nvar _c;\n$RefreshReg$(_c, \"LandingPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/landing/page.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/components/landing/CookieBanner.js":
/*!************************************************!*\
  !*** ./src/components/landing/CookieBanner.js ***!
  \************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/link */ \"(app-pages-browser)/./node_modules/next/dist/client/app-dir/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _barrel_optimize_names_Check_Cookie_Settings_X_lucide_react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=Check,Cookie,Settings,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/cookie.js\");\n/* harmony import */ var _barrel_optimize_names_Check_Cookie_Settings_X_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=Check,Cookie,Settings,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/settings.js\");\n/* harmony import */ var _barrel_optimize_names_Check_Cookie_Settings_X_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=Check,Cookie,Settings,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/x.js\");\n/* harmony import */ var _barrel_optimize_names_Check_Cookie_Settings_X_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=Check,Cookie,Settings,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/check.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\nconst CookieBanner = ()=>{\n    _s();\n    const [showBanner, setShowBanner] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [showSettings, setShowSettings] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [preferences, setPreferences] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        essential: true,\n        analytics: false,\n        marketing: false,\n        functional: false\n    });\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"CookieBanner.useEffect\": ()=>{\n            // Check if user has already made a choice\n            const cookieConsent = localStorage.getItem('cookieConsent');\n            if (!cookieConsent) {\n                setShowBanner(true);\n            }\n        }\n    }[\"CookieBanner.useEffect\"], []);\n    const handleAcceptAll = ()=>{\n        const consent = {\n            essential: true,\n            analytics: true,\n            marketing: true,\n            functional: true,\n            timestamp: new Date().toISOString()\n        };\n        localStorage.setItem('cookieConsent', JSON.stringify(consent));\n        setShowBanner(false);\n        // Initialize analytics and marketing cookies here\n        initializeAnalytics();\n        initializeMarketing();\n        initializeFunctional();\n    };\n    const handleAcceptEssential = ()=>{\n        const consent = {\n            essential: true,\n            analytics: false,\n            marketing: false,\n            functional: false,\n            timestamp: new Date().toISOString()\n        };\n        localStorage.setItem('cookieConsent', JSON.stringify(consent));\n        setShowBanner(false);\n    };\n    const handleSavePreferences = ()=>{\n        const consent = {\n            ...preferences,\n            timestamp: new Date().toISOString()\n        };\n        localStorage.setItem('cookieConsent', JSON.stringify(consent));\n        setShowBanner(false);\n        setShowSettings(false);\n        // Initialize services based on preferences\n        if (preferences.analytics) initializeAnalytics();\n        if (preferences.marketing) initializeMarketing();\n        if (preferences.functional) initializeFunctional();\n    };\n    const initializeAnalytics = ()=>{\n        // Initialize Google Analytics\n        if ( true && window.gtag) {\n            window.gtag('consent', 'update', {\n                analytics_storage: 'granted'\n            });\n        }\n    };\n    const initializeMarketing = ()=>{\n        // Initialize marketing cookies\n        if ( true && window.gtag) {\n            window.gtag('consent', 'update', {\n                ad_storage: 'granted'\n            });\n        }\n    };\n    const initializeFunctional = ()=>{\n        // Initialize functional cookies\n        console.log('Functional cookies enabled');\n    };\n    const handlePreferenceChange = (type)=>{\n        if (type === 'essential') return; // Cannot disable essential cookies\n        setPreferences((prev)=>({\n                ...prev,\n                [type]: !prev[type]\n            }));\n    };\n    if (!showBanner) return null;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"fixed bottom-0 left-0 right-0 bg-white dark:bg-gray-800 border-t border-gray-200 dark:border-gray-700 shadow-lg z-50\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"container mx-auto px-4 py-4\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex flex-col lg:flex-row items-start lg:items-center justify-between gap-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-start space-x-3 flex-1\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Check_Cookie_Settings_X_lucide_react__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                        className: \"text-primary-600 mt-1 flex-shrink-0\",\n                                        size: 24\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\landing\\\\CookieBanner.js\",\n                                        lineNumber: 113,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"text-lg font-semibold text-gray-900 dark:text-white mb-2\",\n                                                children: \"\\uD83C\\uDF6A Utilizamos Cookies\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\landing\\\\CookieBanner.js\",\n                                                lineNumber: 115,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-gray-700 dark:text-gray-300 text-sm\",\n                                                children: [\n                                                    \"Utilizamos cookies para melhorar sua experi\\xeancia, personalizar conte\\xfado e analisar nosso tr\\xe1fego. Ao continuar navegando, voc\\xea concorda com nossa\",\n                                                    ' ',\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                                        href: \"/legal/politica-cookies\",\n                                                        className: \"text-primary-600 hover:text-primary-700 underline\",\n                                                        children: \"Pol\\xedtica de Cookies\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\landing\\\\CookieBanner.js\",\n                                                        lineNumber: 121,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    ' ',\n                                                    \"e\",\n                                                    ' ',\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                                        href: \"/legal/politica-privacidade\",\n                                                        className: \"text-primary-600 hover:text-primary-700 underline\",\n                                                        children: \"Pol\\xedtica de Privacidade\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\landing\\\\CookieBanner.js\",\n                                                        lineNumber: 125,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    \".\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\landing\\\\CookieBanner.js\",\n                                                lineNumber: 118,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\landing\\\\CookieBanner.js\",\n                                        lineNumber: 114,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\landing\\\\CookieBanner.js\",\n                                lineNumber: 112,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex flex-col sm:flex-row gap-3 w-full lg:w-auto\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: ()=>setShowSettings(true),\n                                        className: \"flex items-center justify-center px-4 py-2 border border-gray-300 dark:border-gray-600 text-gray-700 dark:text-gray-300 rounded-md hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Check_Cookie_Settings_X_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                                size: 16,\n                                                className: \"mr-2\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\landing\\\\CookieBanner.js\",\n                                                lineNumber: 138,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            \"Personalizar\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\landing\\\\CookieBanner.js\",\n                                        lineNumber: 134,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: handleAcceptEssential,\n                                        className: \"px-4 py-2 border border-gray-300 dark:border-gray-600 text-gray-700 dark:text-gray-300 rounded-md hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors\",\n                                        children: \"Apenas Essenciais\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\landing\\\\CookieBanner.js\",\n                                        lineNumber: 141,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: handleAcceptAll,\n                                        className: \"px-4 py-2 bg-primary-600 text-white rounded-md hover:bg-primary-700 transition-colors\",\n                                        children: \"Aceitar Todos\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\landing\\\\CookieBanner.js\",\n                                        lineNumber: 147,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\landing\\\\CookieBanner.js\",\n                                lineNumber: 133,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\landing\\\\CookieBanner.js\",\n                        lineNumber: 111,\n                        columnNumber: 11\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\landing\\\\CookieBanner.js\",\n                    lineNumber: 110,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\landing\\\\CookieBanner.js\",\n                lineNumber: 109,\n                columnNumber: 7\n            }, undefined),\n            showSettings && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"bg-white dark:bg-gray-800 rounded-lg shadow-xl max-w-2xl w-full max-h-[90vh] overflow-y-auto\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"p-6\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center justify-between mb-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                        className: \"text-2xl font-bold text-gray-900 dark:text-white\",\n                                        children: \"Configura\\xe7\\xf5es de Cookies\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\landing\\\\CookieBanner.js\",\n                                        lineNumber: 164,\n                                        columnNumber: 17\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: ()=>setShowSettings(false),\n                                        className: \"text-gray-400 hover:text-gray-600 dark:hover:text-gray-300\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Check_Cookie_Settings_X_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                            size: 24\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\landing\\\\CookieBanner.js\",\n                                            lineNumber: 171,\n                                            columnNumber: 19\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\landing\\\\CookieBanner.js\",\n                                        lineNumber: 167,\n                                        columnNumber: 17\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\landing\\\\CookieBanner.js\",\n                                lineNumber: 163,\n                                columnNumber: 15\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"space-y-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-gray-700 dark:text-gray-300\",\n                                        children: \"Personalize suas prefer\\xeancias de cookies. Voc\\xea pode alterar essas configura\\xe7\\xf5es a qualquer momento.\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\landing\\\\CookieBanner.js\",\n                                        lineNumber: 176,\n                                        columnNumber: 17\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"border border-gray-200 dark:border-gray-700 rounded-lg p-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center justify-between mb-3\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                        className: \"text-lg font-semibold text-gray-900 dark:text-white\",\n                                                        children: \"Cookies Essenciais\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\landing\\\\CookieBanner.js\",\n                                                        lineNumber: 183,\n                                                        columnNumber: 21\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Check_Cookie_Settings_X_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                                                className: \"text-green-500 mr-2\",\n                                                                size: 20\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\landing\\\\CookieBanner.js\",\n                                                                lineNumber: 187,\n                                                                columnNumber: 23\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-sm text-gray-500 dark:text-gray-400\",\n                                                                children: \"Sempre ativo\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\landing\\\\CookieBanner.js\",\n                                                                lineNumber: 188,\n                                                                columnNumber: 23\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\landing\\\\CookieBanner.js\",\n                                                        lineNumber: 186,\n                                                        columnNumber: 21\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\landing\\\\CookieBanner.js\",\n                                                lineNumber: 182,\n                                                columnNumber: 19\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-gray-600 dark:text-gray-400 text-sm\",\n                                                children: \"Necess\\xe1rios para o funcionamento b\\xe1sico do site. Incluem autentica\\xe7\\xe3o, seguran\\xe7a e prefer\\xeancias b\\xe1sicas.\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\landing\\\\CookieBanner.js\",\n                                                lineNumber: 191,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\landing\\\\CookieBanner.js\",\n                                        lineNumber: 181,\n                                        columnNumber: 17\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"border border-gray-200 dark:border-gray-700 rounded-lg p-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center justify-between mb-3\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                        className: \"text-lg font-semibold text-gray-900 dark:text-white\",\n                                                        children: \"Cookies de An\\xe1lise\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\landing\\\\CookieBanner.js\",\n                                                        lineNumber: 199,\n                                                        columnNumber: 21\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                        className: \"relative inline-flex items-center cursor-pointer\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                type: \"checkbox\",\n                                                                checked: preferences.analytics,\n                                                                onChange: ()=>handlePreferenceChange('analytics'),\n                                                                className: \"sr-only peer\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\landing\\\\CookieBanner.js\",\n                                                                lineNumber: 203,\n                                                                columnNumber: 23\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-primary-300 dark:peer-focus:ring-primary-800 rounded-full peer dark:bg-gray-700 peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all dark:border-gray-600 peer-checked:bg-primary-600\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\landing\\\\CookieBanner.js\",\n                                                                lineNumber: 209,\n                                                                columnNumber: 23\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\landing\\\\CookieBanner.js\",\n                                                        lineNumber: 202,\n                                                        columnNumber: 21\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\landing\\\\CookieBanner.js\",\n                                                lineNumber: 198,\n                                                columnNumber: 19\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-gray-600 dark:text-gray-400 text-sm\",\n                                                children: \"Nos ajudam a entender como voc\\xea usa nosso site para melhorar a experi\\xeancia. Incluem Google Analytics.\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\landing\\\\CookieBanner.js\",\n                                                lineNumber: 212,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\landing\\\\CookieBanner.js\",\n                                        lineNumber: 197,\n                                        columnNumber: 17\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"border border-gray-200 dark:border-gray-700 rounded-lg p-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center justify-between mb-3\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                        className: \"text-lg font-semibold text-gray-900 dark:text-white\",\n                                                        children: \"Cookies de Marketing\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\landing\\\\CookieBanner.js\",\n                                                        lineNumber: 220,\n                                                        columnNumber: 21\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                        className: \"relative inline-flex items-center cursor-pointer\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                type: \"checkbox\",\n                                                                checked: preferences.marketing,\n                                                                onChange: ()=>handlePreferenceChange('marketing'),\n                                                                className: \"sr-only peer\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\landing\\\\CookieBanner.js\",\n                                                                lineNumber: 224,\n                                                                columnNumber: 23\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-primary-300 dark:peer-focus:ring-primary-800 rounded-full peer dark:bg-gray-700 peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all dark:border-gray-600 peer-checked:bg-primary-600\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\landing\\\\CookieBanner.js\",\n                                                                lineNumber: 230,\n                                                                columnNumber: 23\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\landing\\\\CookieBanner.js\",\n                                                        lineNumber: 223,\n                                                        columnNumber: 21\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\landing\\\\CookieBanner.js\",\n                                                lineNumber: 219,\n                                                columnNumber: 19\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-gray-600 dark:text-gray-400 text-sm\",\n                                                children: \"Utilizados para personalizar an\\xfancios e conte\\xfado. Incluem pixels de redes sociais e ferramentas de remarketing.\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\landing\\\\CookieBanner.js\",\n                                                lineNumber: 233,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\landing\\\\CookieBanner.js\",\n                                        lineNumber: 218,\n                                        columnNumber: 17\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"border border-gray-200 dark:border-gray-700 rounded-lg p-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center justify-between mb-3\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                        className: \"text-lg font-semibold text-gray-900 dark:text-white\",\n                                                        children: \"Cookies Funcionais\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\landing\\\\CookieBanner.js\",\n                                                        lineNumber: 241,\n                                                        columnNumber: 21\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                        className: \"relative inline-flex items-center cursor-pointer\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                type: \"checkbox\",\n                                                                checked: preferences.functional,\n                                                                onChange: ()=>handlePreferenceChange('functional'),\n                                                                className: \"sr-only peer\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\landing\\\\CookieBanner.js\",\n                                                                lineNumber: 245,\n                                                                columnNumber: 23\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-primary-300 dark:peer-focus:ring-primary-800 rounded-full peer dark:bg-gray-700 peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all dark:border-gray-600 peer-checked:bg-primary-600\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\landing\\\\CookieBanner.js\",\n                                                                lineNumber: 251,\n                                                                columnNumber: 23\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\landing\\\\CookieBanner.js\",\n                                                        lineNumber: 244,\n                                                        columnNumber: 21\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\landing\\\\CookieBanner.js\",\n                                                lineNumber: 240,\n                                                columnNumber: 19\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-gray-600 dark:text-gray-400 text-sm\",\n                                                children: \"Melhoram a funcionalidade do site, como lembrar suas prefer\\xeancias e configura\\xe7\\xf5es personalizadas.\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\landing\\\\CookieBanner.js\",\n                                                lineNumber: 254,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\landing\\\\CookieBanner.js\",\n                                        lineNumber: 239,\n                                        columnNumber: 17\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\landing\\\\CookieBanner.js\",\n                                lineNumber: 175,\n                                columnNumber: 15\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex flex-col sm:flex-row gap-3 mt-8\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: ()=>setShowSettings(false),\n                                        className: \"flex-1 px-4 py-2 border border-gray-300 dark:border-gray-600 text-gray-700 dark:text-gray-300 rounded-md hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors\",\n                                        children: \"Cancelar\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\landing\\\\CookieBanner.js\",\n                                        lineNumber: 261,\n                                        columnNumber: 17\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: handleSavePreferences,\n                                        className: \"flex-1 px-4 py-2 bg-primary-600 text-white rounded-md hover:bg-primary-700 transition-colors\",\n                                        children: \"Salvar Prefer\\xeancias\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\landing\\\\CookieBanner.js\",\n                                        lineNumber: 267,\n                                        columnNumber: 17\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\landing\\\\CookieBanner.js\",\n                                lineNumber: 260,\n                                columnNumber: 15\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"mt-4 text-center\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                    href: \"/legal/politica-cookies\",\n                                    className: \"text-primary-600 hover:text-primary-700 text-sm underline\",\n                                    children: \"Saiba mais sobre nossa Pol\\xedtica de Cookies\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\landing\\\\CookieBanner.js\",\n                                    lineNumber: 276,\n                                    columnNumber: 17\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\landing\\\\CookieBanner.js\",\n                                lineNumber: 275,\n                                columnNumber: 15\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\landing\\\\CookieBanner.js\",\n                        lineNumber: 162,\n                        columnNumber: 13\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\landing\\\\CookieBanner.js\",\n                    lineNumber: 161,\n                    columnNumber: 11\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\landing\\\\CookieBanner.js\",\n                lineNumber: 160,\n                columnNumber: 9\n            }, undefined)\n        ]\n    }, void 0, true);\n};\n_s(CookieBanner, \"+1YsWolOFYHBXi88gqms6ASS1ls=\");\n_c = CookieBanner;\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (CookieBanner);\nvar _c;\n$RefreshReg$(_c, \"CookieBanner\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/landing/CookieBanner.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/cookie.js":
/*!************************************************************!*\
  !*** ./node_modules/lucide-react/dist/esm/icons/cookie.js ***!
  \************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   __iconNode: () => (/* binding */ __iconNode),\n/* harmony export */   \"default\": () => (/* binding */ Cookie)\n/* harmony export */ });\n/* harmony import */ var _createLucideIcon_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../createLucideIcon.js */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/createLucideIcon.js\");\n/**\n * @license lucide-react v0.474.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */ \nconst __iconNode = [\n    [\n        \"path\",\n        {\n            d: \"M12 2a10 10 0 1 0 10 10 4 4 0 0 1-5-5 4 4 0 0 1-5-5\",\n            key: \"laymnq\"\n        }\n    ],\n    [\n        \"path\",\n        {\n            d: \"M8.5 8.5v.01\",\n            key: \"ue8clq\"\n        }\n    ],\n    [\n        \"path\",\n        {\n            d: \"M16 15.5v.01\",\n            key: \"14dtrp\"\n        }\n    ],\n    [\n        \"path\",\n        {\n            d: \"M12 12v.01\",\n            key: \"u5ubse\"\n        }\n    ],\n    [\n        \"path\",\n        {\n            d: \"M11 17v.01\",\n            key: \"1hyl5a\"\n        }\n    ],\n    [\n        \"path\",\n        {\n            d: \"M7 14v.01\",\n            key: \"uct60s\"\n        }\n    ]\n];\nconst Cookie = (0,_createLucideIcon_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(\"Cookie\", __iconNode);\n //# sourceMappingURL=cookie.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9sdWNpZGUtcmVhY3QvZGlzdC9lc20vaWNvbnMvY29va2llLmpzIiwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7OztBQUdPLE1BQU0sVUFBdUI7SUFDbEM7UUFBQyxNQUFRO1FBQUE7WUFBRSxHQUFHLENBQXVEO1lBQUEsS0FBSztRQUFBLENBQVU7S0FBQTtJQUNwRjtRQUFDLE1BQVE7UUFBQTtZQUFFLEdBQUcsQ0FBZ0I7WUFBQSxLQUFLO1FBQUEsQ0FBVTtLQUFBO0lBQzdDO1FBQUMsTUFBUTtRQUFBO1lBQUUsR0FBRyxDQUFnQjtZQUFBLEtBQUs7UUFBQSxDQUFVO0tBQUE7SUFDN0M7UUFBQyxNQUFRO1FBQUE7WUFBRSxHQUFHLENBQWM7WUFBQSxLQUFLO1FBQUEsQ0FBVTtLQUFBO0lBQzNDO1FBQUMsTUFBUTtRQUFBO1lBQUUsR0FBRyxDQUFjO1lBQUEsS0FBSztRQUFBLENBQVU7S0FBQTtJQUMzQztRQUFDLE1BQVE7UUFBQTtZQUFFLEdBQUcsQ0FBYTtZQUFBLEtBQUs7UUFBQSxDQUFVO0tBQUE7Q0FDNUM7QUFhTSxhQUFTLGtFQUFpQixXQUFVLENBQVUiLCJzb3VyY2VzIjpbIkM6XFxVc2Vyc1xcYXJtYW5cXHNyY1xcaWNvbnNcXGNvb2tpZS50cyJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgY3JlYXRlTHVjaWRlSWNvbiBmcm9tICcuLi9jcmVhdGVMdWNpZGVJY29uJztcbmltcG9ydCB7IEljb25Ob2RlIH0gZnJvbSAnLi4vdHlwZXMnO1xuXG5leHBvcnQgY29uc3QgX19pY29uTm9kZTogSWNvbk5vZGUgPSBbXG4gIFsncGF0aCcsIHsgZDogJ00xMiAyYTEwIDEwIDAgMSAwIDEwIDEwIDQgNCAwIDAgMS01LTUgNCA0IDAgMCAxLTUtNScsIGtleTogJ2xheW1ucScgfV0sXG4gIFsncGF0aCcsIHsgZDogJ004LjUgOC41di4wMScsIGtleTogJ3VlOGNscScgfV0sXG4gIFsncGF0aCcsIHsgZDogJ00xNiAxNS41di4wMScsIGtleTogJzE0ZHRycCcgfV0sXG4gIFsncGF0aCcsIHsgZDogJ00xMiAxMnYuMDEnLCBrZXk6ICd1NXVic2UnIH1dLFxuICBbJ3BhdGgnLCB7IGQ6ICdNMTEgMTd2LjAxJywga2V5OiAnMWh5bDVhJyB9XSxcbiAgWydwYXRoJywgeyBkOiAnTTcgMTR2LjAxJywga2V5OiAndWN0NjBzJyB9XSxcbl07XG5cbi8qKlxuICogQGNvbXBvbmVudCBAbmFtZSBDb29raWVcbiAqIEBkZXNjcmlwdGlvbiBMdWNpZGUgU1ZHIGljb24gY29tcG9uZW50LCByZW5kZXJzIFNWRyBFbGVtZW50IHdpdGggY2hpbGRyZW4uXG4gKlxuICogQHByZXZpZXcgIVtpbWddKGRhdGE6aW1hZ2Uvc3ZnK3htbDtiYXNlNjQsUEhOMlp5QWdlRzFzYm5NOUltaDBkSEE2THk5M2QzY3Vkek11YjNKbkx6SXdNREF2YzNabklnb2dJSGRwWkhSb1BTSXlOQ0lLSUNCb1pXbG5hSFE5SWpJMElnb2dJSFpwWlhkQ2IzZzlJakFnTUNBeU5DQXlOQ0lLSUNCbWFXeHNQU0p1YjI1bElnb2dJSE4wY205clpUMGlJekF3TUNJZ2MzUjViR1U5SW1KaFkydG5jbTkxYm1RdFkyOXNiM0k2SUNObVptWTdJR0p2Y21SbGNpMXlZV1JwZFhNNklESndlQ0lLSUNCemRISnZhMlV0ZDJsa2RHZzlJaklpQ2lBZ2MzUnliMnRsTFd4cGJtVmpZWEE5SW5KdmRXNWtJZ29nSUhOMGNtOXJaUzFzYVc1bGFtOXBiajBpY205MWJtUWlDajRLSUNBOGNHRjBhQ0JrUFNKTk1USWdNbUV4TUNBeE1DQXdJREVnTUNBeE1DQXhNQ0EwSURRZ01DQXdJREV0TlMwMUlEUWdOQ0F3SURBZ01TMDFMVFVpSUM4K0NpQWdQSEJoZEdnZ1pEMGlUVGd1TlNBNExqVjJMakF4SWlBdlBnb2dJRHh3WVhSb0lHUTlJazB4TmlBeE5TNDFkaTR3TVNJZ0x6NEtJQ0E4Y0dGMGFDQmtQU0pOTVRJZ01USjJMakF4SWlBdlBnb2dJRHh3WVhSb0lHUTlJazB4TVNBeE4zWXVNREVpSUM4K0NpQWdQSEJoZEdnZ1pEMGlUVGNnTVRSMkxqQXhJaUF2UGdvOEwzTjJaejRLKSAtIGh0dHBzOi8vbHVjaWRlLmRldi9pY29ucy9jb29raWVcbiAqIEBzZWUgaHR0cHM6Ly9sdWNpZGUuZGV2L2d1aWRlL3BhY2thZ2VzL2x1Y2lkZS1yZWFjdCAtIERvY3VtZW50YXRpb25cbiAqXG4gKiBAcGFyYW0ge09iamVjdH0gcHJvcHMgLSBMdWNpZGUgaWNvbnMgcHJvcHMgYW5kIGFueSB2YWxpZCBTVkcgYXR0cmlidXRlXG4gKiBAcmV0dXJucyB7SlNYLkVsZW1lbnR9IEpTWCBFbGVtZW50XG4gKlxuICovXG5jb25zdCBDb29raWUgPSBjcmVhdGVMdWNpZGVJY29uKCdDb29raWUnLCBfX2ljb25Ob2RlKTtcblxuZXhwb3J0IGRlZmF1bHQgQ29va2llO1xuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/cookie.js\n"));

/***/ })

});