'use client';

import { Calendar, Shield, Lock, Server, Eye, <PERSON><PERSON><PERSON>riangle, CheckCircle } from 'lucide-react';

export default function PoliticaSeguranca() {
  return (
    <div className="p-8">
      <div className="max-w-4xl mx-auto">
        {/* Header */}
        <div className="mb-8">
          <h1 className="text-3xl font-bold text-gray-900 dark:text-white mb-4">
            Política de Segurança
          </h1>
          <div className="flex items-center space-x-4 text-sm text-gray-600 dark:text-gray-400">
            <div className="flex items-center space-x-1">
              <Calendar size={16} />
              <span>Última atualização: 18 de junho de 2025</span>
            </div>
            <div className="flex items-center space-x-1">
              <span className="w-2 h-2 bg-green-500 rounded-full"></span>
              <span>Versão 1.0</span>
            </div>
          </div>
        </div>

        {/* Introduction */}
        <section className="mb-8">
          <div className="bg-red-50 dark:bg-red-900/20 p-6 rounded-lg mb-6">
            <div className="flex items-start space-x-3">
              <Shield className="text-red-600 mt-1" size={24} />
              <div>
                <h2 className="text-xl font-semibold text-red-900 dark:text-red-100 mb-3">
                  Compromisso com a Segurança
                </h2>
                <p className="text-red-800 dark:text-red-200">
                  A segurança dos dados é nossa prioridade máxima. Esta política descreve as medidas técnicas 
                  e organizacionais implementadas pela High Tide Systems para proteger as informações de nossos 
                  usuários e garantir a integridade, confidencialidade e disponibilidade de nossa plataforma.
                </p>
              </div>
            </div>
          </div>
        </section>

        {/* Security Status */}
        <section className="mb-8">
          <h2 className="text-2xl font-semibold text-gray-900 dark:text-white mb-4">
            Status de Segurança Atual
          </h2>
          <div className="grid md:grid-cols-3 gap-4">
            <div className="bg-green-50 dark:bg-green-900/20 p-4 rounded-lg text-center">
              <CheckCircle className="mx-auto text-green-600 mb-2" size={32} />
              <h3 className="font-semibold text-green-900 dark:text-green-100 mb-1">SSL/TLS Ativo</h3>
              <p className="text-green-800 dark:text-green-200 text-sm">Certificado válido até 2026</p>
            </div>
            <div className="bg-green-50 dark:bg-green-900/20 p-4 rounded-lg text-center">
              <CheckCircle className="mx-auto text-green-600 mb-2" size={32} />
              <h3 className="font-semibold text-green-900 dark:text-green-100 mb-1">Backup Diário</h3>
              <p className="text-green-800 dark:text-green-200 text-sm">Último backup: hoje às 03:00</p>
            </div>
            <div className="bg-green-50 dark:bg-green-900/20 p-4 rounded-lg text-center">
              <CheckCircle className="mx-auto text-green-600 mb-2" size={32} />
              <h3 className="font-semibold text-green-900 dark:text-green-100 mb-1">Monitoramento 24/7</h3>
              <p className="text-green-800 dark:text-green-200 text-sm">Sistema ativo e funcionando</p>
            </div>
          </div>
        </section>

        {/* Table of Contents */}
        <section className="mb-8">
          <h2 className="text-2xl font-semibold text-gray-900 dark:text-white mb-4">
            Índice
          </h2>
          <div className="bg-gray-50 dark:bg-gray-700 p-6 rounded-lg">
            <ol className="space-y-2 text-sm">
              <li><a href="#infraestrutura" className="text-primary-600 hover:text-primary-700">1. Infraestrutura e Hospedagem</a></li>
              <li><a href="#criptografia" className="text-primary-600 hover:text-primary-700">2. Criptografia e Proteção de Dados</a></li>
              <li><a href="#acesso" className="text-primary-600 hover:text-primary-700">3. Controle de Acesso</a></li>
              <li><a href="#monitoramento" className="text-primary-600 hover:text-primary-700">4. Monitoramento e Detecção</a></li>
              <li><a href="#backup" className="text-primary-600 hover:text-primary-700">5. Backup e Recuperação</a></li>
              <li><a href="#incidentes" className="text-primary-600 hover:text-primary-700">6. Resposta a Incidentes</a></li>
              <li><a href="#auditoria" className="text-primary-600 hover:text-primary-700">7. Auditoria e Conformidade</a></li>
              <li><a href="#responsabilidades" className="text-primary-600 hover:text-primary-700">8. Responsabilidades do Usuário</a></li>
            </ol>
          </div>
        </section>

        {/* Section 1 - Infraestrutura */}
        <section id="infraestrutura" className="mb-8">
          <h2 className="text-2xl font-semibold text-gray-900 dark:text-white mb-4">
            1. Infraestrutura e Hospedagem
          </h2>
          <div className="space-y-6">
            <div className="grid md:grid-cols-2 gap-6">
              <div className="bg-blue-50 dark:bg-blue-900/20 p-4 rounded-lg">
                <div className="flex items-start space-x-3">
                  <Server className="text-blue-600 mt-1" size={20} />
                  <div>
                    <h3 className="font-semibold text-blue-900 dark:text-blue-100 mb-3">Hospedagem Segura</h3>
                    <ul className="list-disc list-inside space-y-1 text-blue-800 dark:text-blue-200 text-sm">
                      <li>Servidores em data centers certificados ISO 27001</li>
                      <li>Redundância geográfica com múltiplas regiões</li>
                      <li>Proteção física 24/7 dos data centers</li>
                      <li>Controle de acesso biométrico às instalações</li>
                      <li>Sistemas de energia e refrigeração redundantes</li>
                    </ul>
                  </div>
                </div>
              </div>
              
              <div className="bg-green-50 dark:bg-green-900/20 p-4 rounded-lg">
                <div className="flex items-start space-x-3">
                  <Shield className="text-green-600 mt-1" size={20} />
                  <div>
                    <h3 className="font-semibold text-green-900 dark:text-green-100 mb-3">Proteção de Rede</h3>
                    <ul className="list-disc list-inside space-y-1 text-green-800 dark:text-green-200 text-sm">
                      <li>Firewall de aplicação web (WAF) ativo</li>
                      <li>Proteção contra ataques DDoS</li>
                      <li>Filtragem de tráfego malicioso</li>
                      <li>Rede privada virtual (VPN) para acesso interno</li>
                      <li>Segmentação de rede por camadas</li>
                    </ul>
                  </div>
                </div>
              </div>
            </div>

            <div className="bg-gray-50 dark:bg-gray-700 p-4 rounded-lg">
              <h4 className="font-semibold text-gray-900 dark:text-white mb-2">Certificações e Conformidade</h4>
              <div className="grid md:grid-cols-4 gap-4 text-center">
                <div className="bg-white dark:bg-gray-800 p-3 rounded">
                  <div className="text-2xl mb-1">🏆</div>
                  <div className="text-sm font-medium text-gray-900 dark:text-white">ISO 27001</div>
                  <div className="text-xs text-gray-600 dark:text-gray-400">Gestão de Segurança</div>
                </div>
                <div className="bg-white dark:bg-gray-800 p-3 rounded">
                  <div className="text-2xl mb-1">🛡️</div>
                  <div className="text-sm font-medium text-gray-900 dark:text-white">SOC 2</div>
                  <div className="text-xs text-gray-600 dark:text-gray-400">Controles de Segurança</div>
                </div>
                <div className="bg-white dark:bg-gray-800 p-3 rounded">
                  <div className="text-2xl mb-1">🔒</div>
                  <div className="text-sm font-medium text-gray-900 dark:text-white">LGPD</div>
                  <div className="text-xs text-gray-600 dark:text-gray-400">Proteção de Dados</div>
                </div>
                <div className="bg-white dark:bg-gray-800 p-3 rounded">
                  <div className="text-2xl mb-1">✅</div>
                  <div className="text-sm font-medium text-gray-900 dark:text-white">PCI DSS</div>
                  <div className="text-xs text-gray-600 dark:text-gray-400">Pagamentos Seguros</div>
                </div>
              </div>
            </div>
          </div>
        </section>

        {/* Section 2 - Criptografia */}
        <section id="criptografia" className="mb-8">
          <h2 className="text-2xl font-semibold text-gray-900 dark:text-white mb-4">
            2. Criptografia e Proteção de Dados
          </h2>
          <div className="space-y-6">
            <div className="grid md:grid-cols-2 gap-6">
              <div className="bg-purple-50 dark:bg-purple-900/20 p-4 rounded-lg">
                <div className="flex items-start space-x-3">
                  <Lock className="text-purple-600 mt-1" size={20} />
                  <div>
                    <h3 className="font-semibold text-purple-900 dark:text-purple-100 mb-3">Dados em Trânsito</h3>
                    <ul className="list-disc list-inside space-y-1 text-purple-800 dark:text-purple-200 text-sm">
                      <li>TLS 1.3 para todas as comunicações</li>
                      <li>Certificados SSL com validação estendida</li>
                      <li>HSTS (HTTP Strict Transport Security)</li>
                      <li>Perfect Forward Secrecy (PFS)</li>
                      <li>Criptografia de ponta a ponta para dados sensíveis</li>
                    </ul>
                  </div>
                </div>
              </div>
              
              <div className="bg-orange-50 dark:bg-orange-900/20 p-4 rounded-lg">
                <div className="flex items-start space-x-3">
                  <Server className="text-orange-600 mt-1" size={20} />
                  <div>
                    <h3 className="font-semibold text-orange-900 dark:text-orange-100 mb-3">Dados em Repouso</h3>
                    <ul className="list-disc list-inside space-y-1 text-orange-800 dark:text-orange-200 text-sm">
                      <li>AES-256 para criptografia de banco de dados</li>
                      <li>Chaves de criptografia rotacionadas regularmente</li>
                      <li>Armazenamento seguro de chaves (HSM)</li>
                      <li>Criptografia de backups e logs</li>
                      <li>Dados sensíveis com criptografia adicional</li>
                    </ul>
                  </div>
                </div>
              </div>
            </div>

            <div className="bg-red-50 dark:bg-red-900/20 p-4 rounded-lg">
              <h4 className="font-semibold text-red-900 dark:text-red-100 mb-2">
                🔐 Algoritmos de Criptografia Utilizados
              </h4>
              <div className="grid md:grid-cols-3 gap-4 text-sm">
                <div>
                  <strong className="text-red-900 dark:text-red-100">Simétrica:</strong>
                  <ul className="list-disc list-inside mt-1 text-red-800 dark:text-red-200">
                    <li>AES-256-GCM</li>
                    <li>ChaCha20-Poly1305</li>
                  </ul>
                </div>
                <div>
                  <strong className="text-red-900 dark:text-red-100">Assimétrica:</strong>
                  <ul className="list-disc list-inside mt-1 text-red-800 dark:text-red-200">
                    <li>RSA-4096</li>
                    <li>ECDSA P-384</li>
                  </ul>
                </div>
                <div>
                  <strong className="text-red-900 dark:text-red-100">Hash:</strong>
                  <ul className="list-disc list-inside mt-1 text-red-800 dark:text-red-200">
                    <li>SHA-256</li>
                    <li>bcrypt (senhas)</li>
                  </ul>
                </div>
              </div>
            </div>
          </div>
        </section>

        {/* Section 3 - Controle de Acesso */}
        <section id="acesso" className="mb-8">
          <h2 className="text-2xl font-semibold text-gray-900 dark:text-white mb-4">
            3. Controle de Acesso
          </h2>
          <div className="space-y-6">
            <div className="grid md:grid-cols-2 gap-6">
              <div className="bg-indigo-50 dark:bg-indigo-900/20 p-4 rounded-lg">
                <h3 className="font-semibold text-indigo-900 dark:text-indigo-100 mb-3">Autenticação</h3>
                <ul className="list-disc list-inside space-y-1 text-indigo-800 dark:text-indigo-200 text-sm">
                  <li>Autenticação multifator (MFA) obrigatória</li>
                  <li>Senhas com política de complexidade</li>
                  <li>Bloqueio automático após tentativas falhadas</li>
                  <li>Sessões com timeout automático</li>
                  <li>Tokens JWT com expiração curta</li>
                </ul>
              </div>
              
              <div className="bg-teal-50 dark:bg-teal-900/20 p-4 rounded-lg">
                <h3 className="font-semibold text-teal-900 dark:text-teal-100 mb-3">Autorização</h3>
                <ul className="list-disc list-inside space-y-1 text-teal-800 dark:text-teal-200 text-sm">
                  <li>Controle de acesso baseado em funções (RBAC)</li>
                  <li>Princípio do menor privilégio</li>
                  <li>Segregação de funções críticas</li>
                  <li>Revisão periódica de permissões</li>
                  <li>Aprovação para acessos elevados</li>
                </ul>
              </div>
            </div>

            <div className="bg-yellow-50 dark:bg-yellow-900/20 p-4 rounded-lg">
              <h4 className="font-semibold text-yellow-900 dark:text-yellow-100 mb-2">
                ⚠️ Políticas de Senha
              </h4>
              <div className="grid md:grid-cols-2 gap-4 text-sm">
                <div>
                  <strong className="text-yellow-900 dark:text-yellow-100">Requisitos Mínimos:</strong>
                  <ul className="list-disc list-inside mt-1 text-yellow-800 dark:text-yellow-200">
                    <li>Mínimo de 12 caracteres</li>
                    <li>Combinação de letras, números e símbolos</li>
                    <li>Não reutilização das últimas 12 senhas</li>
                    <li>Alteração obrigatória a cada 90 dias</li>
                  </ul>
                </div>
                <div>
                  <strong className="text-yellow-900 dark:text-yellow-100">Proteções:</strong>
                  <ul className="list-disc list-inside mt-1 text-yellow-800 dark:text-yellow-200">
                    <li>Hash bcrypt com salt único</li>
                    <li>Verificação contra listas de senhas vazadas</li>
                    <li>Bloqueio temporário após 5 tentativas</li>
                    <li>Notificação de tentativas suspeitas</li>
                  </ul>
                </div>
              </div>
            </div>
          </div>
        </section>

        {/* Section 4 - Monitoramento */}
        <section id="monitoramento" className="mb-8">
          <h2 className="text-2xl font-semibold text-gray-900 dark:text-white mb-4">
            4. Monitoramento e Detecção
          </h2>
          <div className="space-y-6">
            <div className="grid md:grid-cols-2 gap-6">
              <div className="bg-red-50 dark:bg-red-900/20 p-4 rounded-lg">
                <div className="flex items-start space-x-3">
                  <Eye className="text-red-600 mt-1" size={20} />
                  <div>
                    <h3 className="font-semibold text-red-900 dark:text-red-100 mb-3">Monitoramento 24/7</h3>
                    <ul className="list-disc list-inside space-y-1 text-red-800 dark:text-red-200 text-sm">
                      <li>SIEM (Security Information and Event Management)</li>
                      <li>Detecção de anomalias comportamentais</li>
                      <li>Alertas automáticos para atividades suspeitas</li>
                      <li>Análise de logs em tempo real</li>
                      <li>Correlação de eventos de segurança</li>
                    </ul>
                  </div>
                </div>
              </div>
              
              <div className="bg-blue-50 dark:bg-blue-900/20 p-4 rounded-lg">
                <div className="flex items-start space-x-3">
                  <AlertTriangle className="text-blue-600 mt-1" size={20} />
                  <div>
                    <h3 className="font-semibold text-blue-900 dark:text-blue-100 mb-3">Detecção de Ameaças</h3>
                    <ul className="list-disc list-inside space-y-1 text-blue-800 dark:text-blue-200 text-sm">
                      <li>Inteligência de ameaças atualizada</li>
                      <li>Detecção de malware e vírus</li>
                      <li>Análise de comportamento de usuários</li>
                      <li>Identificação de tentativas de intrusão</li>
                      <li>Monitoramento de integridade de arquivos</li>
                    </ul>
                  </div>
                </div>
              </div>
            </div>

            <div className="bg-green-50 dark:bg-green-900/20 p-4 rounded-lg">
              <h4 className="font-semibold text-green-900 dark:text-green-100 mb-2">
                📊 Métricas de Segurança Monitoradas
              </h4>
              <div className="grid md:grid-cols-4 gap-4 text-center text-sm">
                <div>
                  <div className="text-2xl font-bold text-green-600 mb-1">99.9%</div>
                  <div className="text-green-800 dark:text-green-200">Disponibilidade</div>
                </div>
                <div>
                  <div className="text-2xl font-bold text-green-600 mb-1">&lt;1s</div>
                  <div className="text-green-800 dark:text-green-200">Tempo de Detecção</div>
                </div>
                <div>
                  <div className="text-2xl font-bold text-green-600 mb-1">0</div>
                  <div className="text-green-800 dark:text-green-200">Incidentes Críticos</div>
                </div>
                <div>
                  <div className="text-2xl font-bold text-green-600 mb-1">24/7</div>
                  <div className="text-green-800 dark:text-green-200">Monitoramento</div>
                </div>
              </div>
            </div>
          </div>
        </section>

        {/* Section 5 - Backup */}
        <section id="backup" className="mb-8">
          <h2 className="text-2xl font-semibold text-gray-900 dark:text-white mb-4">
            5. Backup e Recuperação
          </h2>
          <div className="space-y-6">
            <div className="grid md:grid-cols-2 gap-6">
              <div className="bg-blue-50 dark:bg-blue-900/20 p-4 rounded-lg">
                <h3 className="font-semibold text-blue-900 dark:text-blue-100 mb-3">Estratégia de Backup</h3>
                <ul className="list-disc list-inside space-y-1 text-blue-800 dark:text-blue-200 text-sm">
                  <li>Backup automático diário às 03:00 UTC</li>
                  <li>Backup incremental a cada 6 horas</li>
                  <li>Retenção de 30 dias para backups diários</li>
                  <li>Retenção de 12 meses para backups mensais</li>
                  <li>Armazenamento em múltiplas regiões geográficas</li>
                </ul>
              </div>

              <div className="bg-green-50 dark:bg-green-900/20 p-4 rounded-lg">
                <h3 className="font-semibold text-green-900 dark:text-green-100 mb-3">Recuperação de Dados</h3>
                <ul className="list-disc list-inside space-y-1 text-green-800 dark:text-green-200 text-sm">
                  <li>RTO (Recovery Time Objective): 4 horas</li>
                  <li>RPO (Recovery Point Objective): 1 hora</li>
                  <li>Testes de recuperação mensais</li>
                  <li>Procedimentos documentados e testados</li>
                  <li>Equipe treinada para situações de emergência</li>
                </ul>
              </div>
            </div>

            <div className="bg-purple-50 dark:bg-purple-900/20 p-4 rounded-lg">
              <h4 className="font-semibold text-purple-900 dark:text-purple-100 mb-2">
                💾 Tipos de Backup Implementados
              </h4>
              <div className="grid md:grid-cols-3 gap-4 text-sm">
                <div>
                  <strong className="text-purple-900 dark:text-purple-100">Completo:</strong>
                  <ul className="list-disc list-inside mt-1 text-purple-800 dark:text-purple-200">
                    <li>Todos os dados e configurações</li>
                    <li>Executado semanalmente</li>
                    <li>Armazenado por 12 meses</li>
                  </ul>
                </div>
                <div>
                  <strong className="text-purple-900 dark:text-purple-100">Incremental:</strong>
                  <ul className="list-disc list-inside mt-1 text-purple-800 dark:text-purple-200">
                    <li>Apenas dados alterados</li>
                    <li>Executado a cada 6 horas</li>
                    <li>Armazenado por 30 dias</li>
                  </ul>
                </div>
                <div>
                  <strong className="text-purple-900 dark:text-purple-100">Snapshot:</strong>
                  <ul className="list-disc list-inside mt-1 text-purple-800 dark:text-purple-200">
                    <li>Estado instantâneo do sistema</li>
                    <li>Antes de atualizações críticas</li>
                    <li>Recuperação rápida</li>
                  </ul>
                </div>
              </div>
            </div>
          </div>
        </section>

        {/* Section 6 - Incidentes */}
        <section id="incidentes" className="mb-8">
          <h2 className="text-2xl font-semibold text-gray-900 dark:text-white mb-4">
            6. Resposta a Incidentes
          </h2>
          <div className="space-y-6">
            <div className="bg-red-50 dark:bg-red-900/20 p-6 rounded-lg">
              <h3 className="text-lg font-semibold text-red-900 dark:text-red-100 mb-4">
                Plano de Resposta a Incidentes
              </h3>
              <div className="grid md:grid-cols-4 gap-4">
                <div className="text-center">
                  <div className="w-12 h-12 bg-red-600 text-white rounded-full flex items-center justify-center mx-auto mb-2">1</div>
                  <h4 className="font-semibold text-red-900 dark:text-red-100 mb-1">Detecção</h4>
                  <p className="text-red-800 dark:text-red-200 text-sm">Identificação automática ou manual do incidente</p>
                </div>
                <div className="text-center">
                  <div className="w-12 h-12 bg-red-600 text-white rounded-full flex items-center justify-center mx-auto mb-2">2</div>
                  <h4 className="font-semibold text-red-900 dark:text-red-100 mb-1">Contenção</h4>
                  <p className="text-red-800 dark:text-red-200 text-sm">Isolamento e contenção da ameaça</p>
                </div>
                <div className="text-center">
                  <div className="w-12 h-12 bg-red-600 text-white rounded-full flex items-center justify-center mx-auto mb-2">3</div>
                  <h4 className="font-semibold text-red-900 dark:text-red-100 mb-1">Erradicação</h4>
                  <p className="text-red-800 dark:text-red-200 text-sm">Remoção completa da ameaça</p>
                </div>
                <div className="text-center">
                  <div className="w-12 h-12 bg-red-600 text-white rounded-full flex items-center justify-center mx-auto mb-2">4</div>
                  <h4 className="font-semibold text-red-900 dark:text-red-100 mb-1">Recuperação</h4>
                  <p className="text-red-800 dark:text-red-200 text-sm">Restauração dos serviços normais</p>
                </div>
              </div>
            </div>

            <div className="grid md:grid-cols-2 gap-6">
              <div className="bg-orange-50 dark:bg-orange-900/20 p-4 rounded-lg">
                <h3 className="font-semibold text-orange-900 dark:text-orange-100 mb-3">Classificação de Incidentes</h3>
                <div className="space-y-2 text-sm">
                  <div className="flex items-center space-x-2">
                    <div className="w-3 h-3 bg-red-500 rounded-full"></div>
                    <span className="text-orange-800 dark:text-orange-200"><strong>Crítico:</strong> Resposta em 15 minutos</span>
                  </div>
                  <div className="flex items-center space-x-2">
                    <div className="w-3 h-3 bg-orange-500 rounded-full"></div>
                    <span className="text-orange-800 dark:text-orange-200"><strong>Alto:</strong> Resposta em 1 hora</span>
                  </div>
                  <div className="flex items-center space-x-2">
                    <div className="w-3 h-3 bg-yellow-500 rounded-full"></div>
                    <span className="text-orange-800 dark:text-orange-200"><strong>Médio:</strong> Resposta em 4 horas</span>
                  </div>
                  <div className="flex items-center space-x-2">
                    <div className="w-3 h-3 bg-green-500 rounded-full"></div>
                    <span className="text-orange-800 dark:text-orange-200"><strong>Baixo:</strong> Resposta em 24 horas</span>
                  </div>
                </div>
              </div>

              <div className="bg-blue-50 dark:bg-blue-900/20 p-4 rounded-lg">
                <h3 className="font-semibold text-blue-900 dark:text-blue-100 mb-3">Comunicação de Incidentes</h3>
                <ul className="list-disc list-inside space-y-1 text-blue-800 dark:text-blue-200 text-sm">
                  <li>Notificação imediata da equipe de segurança</li>
                  <li>Comunicação aos usuários afetados</li>
                  <li>Relatório à ANPD quando aplicável</li>
                  <li>Documentação completa do incidente</li>
                  <li>Análise post-incidente e melhorias</li>
                </ul>
              </div>
            </div>
          </div>
        </section>

        {/* Section 7 - Auditoria */}
        <section id="auditoria" className="mb-8">
          <h2 className="text-2xl font-semibold text-gray-900 dark:text-white mb-4">
            7. Auditoria e Conformidade
          </h2>
          <div className="space-y-6">
            <div className="grid md:grid-cols-2 gap-6">
              <div className="bg-indigo-50 dark:bg-indigo-900/20 p-4 rounded-lg">
                <h3 className="font-semibold text-indigo-900 dark:text-indigo-100 mb-3">Auditoria Interna</h3>
                <ul className="list-disc list-inside space-y-1 text-indigo-800 dark:text-indigo-200 text-sm">
                  <li>Revisão trimestral de controles de segurança</li>
                  <li>Testes de penetração semestrais</li>
                  <li>Avaliação de vulnerabilidades mensais</li>
                  <li>Revisão de logs e eventos de segurança</li>
                  <li>Verificação de conformidade com políticas</li>
                </ul>
              </div>

              <div className="bg-teal-50 dark:bg-teal-900/20 p-4 rounded-lg">
                <h3 className="font-semibold text-teal-900 dark:text-teal-100 mb-3">Auditoria Externa</h3>
                <ul className="list-disc list-inside space-y-1 text-teal-800 dark:text-teal-200 text-sm">
                  <li>Auditoria anual por empresa certificada</li>
                  <li>Testes de penetração por terceiros</li>
                  <li>Certificação ISO 27001 renovada anualmente</li>
                  <li>Avaliação de conformidade LGPD</li>
                  <li>Relatórios de auditoria disponíveis</li>
                </ul>
              </div>
            </div>

            <div className="bg-gray-50 dark:bg-gray-700 p-4 rounded-lg">
              <h4 className="font-semibold text-gray-900 dark:text-white mb-2">Logs e Rastreabilidade</h4>
              <div className="grid md:grid-cols-3 gap-4 text-sm">
                <div>
                  <strong className="text-gray-900 dark:text-white">Logs de Acesso:</strong>
                  <ul className="list-disc list-inside mt-1 text-gray-700 dark:text-gray-300">
                    <li>Autenticação e autorização</li>
                    <li>Ações administrativas</li>
                    <li>Acesso a dados sensíveis</li>
                  </ul>
                </div>
                <div>
                  <strong className="text-gray-900 dark:text-white">Logs de Sistema:</strong>
                  <ul className="list-disc list-inside mt-1 text-gray-700 dark:text-gray-300">
                    <li>Eventos de segurança</li>
                    <li>Alterações de configuração</li>
                    <li>Performance e disponibilidade</li>
                  </ul>
                </div>
                <div>
                  <strong className="text-gray-900 dark:text-white">Retenção:</strong>
                  <ul className="list-disc list-inside mt-1 text-gray-700 dark:text-gray-300">
                    <li>Logs críticos: 7 anos</li>
                    <li>Logs de acesso: 2 anos</li>
                    <li>Logs de sistema: 1 ano</li>
                  </ul>
                </div>
              </div>
            </div>
          </div>
        </section>

        {/* Section 8 - Responsabilidades */}
        <section id="responsabilidades" className="mb-8">
          <h2 className="text-2xl font-semibold text-gray-900 dark:text-white mb-4">
            8. Responsabilidades do Usuário
          </h2>
          <div className="space-y-6">
            <div className="bg-yellow-50 dark:bg-yellow-900/20 p-6 rounded-lg">
              <h3 className="text-lg font-semibold text-yellow-900 dark:text-yellow-100 mb-4">
                Sua Parte na Segurança
              </h3>
              <p className="text-yellow-800 dark:text-yellow-200 mb-4">
                A segurança é uma responsabilidade compartilhada. Enquanto protegemos nossa infraestrutura,
                você deve seguir as melhores práticas para manter sua conta segura:
              </p>

              <div className="grid md:grid-cols-2 gap-6">
                <div>
                  <h4 className="font-semibold text-yellow-900 dark:text-yellow-100 mb-2">✅ Faça</h4>
                  <ul className="list-disc list-inside space-y-1 text-yellow-800 dark:text-yellow-200 text-sm">
                    <li>Use senhas fortes e únicas</li>
                    <li>Ative a autenticação multifator</li>
                    <li>Mantenha seu dispositivo atualizado</li>
                    <li>Faça logout ao sair</li>
                    <li>Reporte atividades suspeitas</li>
                    <li>Use redes Wi-Fi seguras</li>
                  </ul>
                </div>

                <div>
                  <h4 className="font-semibold text-yellow-900 dark:text-yellow-100 mb-2">❌ Não Faça</h4>
                  <ul className="list-disc list-inside space-y-1 text-yellow-800 dark:text-yellow-200 text-sm">
                    <li>Compartilhe suas credenciais</li>
                    <li>Use a mesma senha em outros sites</li>
                    <li>Acesse de computadores públicos</li>
                    <li>Clique em links suspeitos</li>
                    <li>Baixe arquivos não confiáveis</li>
                    <li>Ignore alertas de segurança</li>
                  </ul>
                </div>
              </div>
            </div>

            <div className="grid md:grid-cols-3 gap-4">
              <div className="bg-red-50 dark:bg-red-900/20 p-4 rounded-lg text-center">
                <div className="text-2xl mb-2">🚨</div>
                <h4 className="font-semibold text-red-900 dark:text-red-100 mb-2">Suspeita de Comprometimento?</h4>
                <p className="text-red-800 dark:text-red-200 text-sm mb-3">
                  Entre em contato imediatamente
                </p>
                <a
                  href="mailto:<EMAIL>"
                  className="inline-block px-3 py-2 bg-red-600 text-white rounded-md hover:bg-red-700 transition-colors text-sm"
                >
                  Reportar Incidente
                </a>
              </div>

              <div className="bg-blue-50 dark:bg-blue-900/20 p-4 rounded-lg text-center">
                <div className="text-2xl mb-2">📚</div>
                <h4 className="font-semibold text-blue-900 dark:text-blue-100 mb-2">Treinamento</h4>
                <p className="text-blue-800 dark:text-blue-200 text-sm mb-3">
                  Materiais de segurança disponíveis
                </p>
                <button className="px-3 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors text-sm">
                  Acessar Guias
                </button>
              </div>

              <div className="bg-green-50 dark:bg-green-900/20 p-4 rounded-lg text-center">
                <div className="text-2xl mb-2">🛡️</div>
                <h4 className="font-semibold text-green-900 dark:text-green-100 mb-2">Status de Segurança</h4>
                <p className="text-green-800 dark:text-green-200 text-sm mb-3">
                  Verifique a segurança da sua conta
                </p>
                <button className="px-3 py-2 bg-green-600 text-white rounded-md hover:bg-green-700 transition-colors text-sm">
                  Verificar Conta
                </button>
              </div>
            </div>
          </div>
        </section>

        {/* Footer da página */}
        <div className="border-t border-gray-200 dark:border-gray-700 pt-8 mt-12">
          <div className="bg-primary-50 dark:bg-primary-900/20 p-6 rounded-lg">
            <h3 className="text-lg font-semibold text-primary-900 dark:text-primary-100 mb-3">
              Contato da Equipe de Segurança
            </h3>
            <p className="text-primary-800 dark:text-primary-200 mb-4">
              Para questões relacionadas à segurança, vulnerabilidades ou incidentes, entre em contato:
            </p>
            <div className="flex flex-wrap gap-4">
              <a
                href="mailto:<EMAIL>"
                className="inline-flex items-center px-4 py-2 bg-red-600 text-white rounded-md hover:bg-red-700 transition-colors"
              >
                🚨 Reportar Incidente
              </a>
              <a
                href="mailto:<EMAIL>"
                className="inline-flex items-center px-4 py-2 bg-primary-600 text-white rounded-md hover:bg-primary-700 transition-colors"
              >
                📧 Contatar DPO
              </a>
              <a
                href="/legal/politica-privacidade"
                className="inline-flex items-center px-4 py-2 border border-primary-300 dark:border-primary-600 text-primary-700 dark:text-primary-300 rounded-md hover:bg-primary-50 dark:hover:bg-primary-900/20 transition-colors"
              >
                📋 Política de Privacidade
              </a>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
