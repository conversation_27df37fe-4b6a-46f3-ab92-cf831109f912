{"name": "@hello-pangea/dnd", "version": "16.6.0", "private": false, "description": "Beautiful and accessible drag and drop for lists with React", "author": "<PERSON> <<EMAIL>>", "maintainers": ["<PERSON> <<EMAIL>>", "<PERSON> <<EMAIL>>"], "keywords": ["drag and drop", "dnd", "sortable", "reorder", "reorderable", "react", "react.js", "natural", "beautiful", "accessible"], "repository": {"type": "git", "url": "https://github.com/hello-pangea/dnd.git"}, "bugs": {"url": "https://github.com/hello-pangea/dnd/issues"}, "main": "dist/dnd.cjs.js", "module": "dist/dnd.esm.js", "types": "dist/dnd.d.ts", "sideEffects": false, "files": ["/dist", "/src"], "publishConfig": {"access": "public"}, "config": {"prettier_target": "*.{js,jsx,ts,tsx,md,json} src/**/*.{js,jsx,ts,tsx,md,json} test/**/*.{js,jsx,ts,tsx,md,json} docs/**/*.{js,jsx,ts,tsx,md,json} stories/**/*.{js,jsx,ts,tsx,md,json} cypress/**/*.{js,jsx,ts,tsx,md,json} csp-server/**/*.{js,jsx,ts,tsx,md,json}"}, "scripts": {"commit": "cz", "chromatic": "chromatic --project-token=f92123f238de", "prepare": "husky", "release": "release-it", "release:test": "release-it --dry-run", "test:accessibility": "lighthouse http://localhost:9002/iframe.html?id=examples-single-vertical-list--basic --no-enable-error-reporting --config-path=lighthouse.config.js --chrome-flags='--headless' --output=json --output=html --output-path=./test-reports/lighthouse/a11y.json && node a11y-audit-parse.js", "test": "jest --config ./jest.config.ts", "test:react-16": "cross-env REACT_MAJOR_VERSION=16 pnpm test", "test:react-17": "cross-env REACT_MAJOR_VERSION=17 pnpm test", "test:react-18": "cross-env REACT_MAJOR_VERSION=18 pnpm test", "test:browser": "cypress open", "test:browser:ci": "cypress run", "test:coverage": "pnpm test --coverage --coveragePathIgnorePatterns=/debug", "validate": "pnpm prettier:check && pnpm lint:eslint && pnpm lint:css && pnpm typecheck", "prettier:check": "pnpm prettier --debug-check $npm_package_config_prettier_target", "prettier:write": "pnpm prettier --write $npm_package_config_prettier_target", "lint:eslint": "pnpm eslint \"./**/*.{js,jsx,ts,tsx}\"", "lint:css": "stylelint \"stories/**/*.{js,jsx,ts,tsx}\"", "typecheck": "pnpm typecheck:lib && pnpm typecheck:test && pnpm typecheck:storybook", "typecheck:lib": "pnpm tsc --project tsconfig.json", "typecheck:storybook": "pnpm tsc --project stories/tsconfig.json && pnpm tsc --project .storybook/tsconfig.json", "typecheck:test": "pnpm tsc --project test/tsconfig.json && pnpm tsc --project csp-server/tsconfig.json && pnpm tsc --project cypress/tsconfig.json", "bundle-size:check": "cross-env SNAPSHOT=match pnpm bundle-size:update", "bundle-size:update": "pnpm build:clean && pnpm build:dist && pnpm build:clean", "build": "pnpm build:clean && pnpm build:dist", "build:clean": "<PERSON><PERSON><PERSON> dist", "build:dist": "cross-env NODE_ENV=production rollup -c", "storybook": "storybook dev -p 9002", "build-storybook": "storybook build -c .storybook -o site", "prepublishOnly": "pnpm build"}, "dependencies": {"@babel/runtime": "^7.24.1", "css-box-model": "^1.2.1", "memoize-one": "^6.0.0", "raf-schd": "^4.0.3", "react-redux": "^8.1.3", "redux": "^4.2.1", "use-memo-one": "^1.1.3"}, "devDependencies": {"@atlaskit/css-reset": "6.7.1", "@atlaskit/theme": "12.6.6", "@babel/core": "7.24.3", "@babel/eslint-parser": "7.24.1", "@babel/plugin-transform-class-properties": "7.24.1", "@babel/plugin-transform-modules-commonjs": "7.24.1", "@babel/plugin-transform-object-assign": "7.24.1", "@babel/plugin-transform-private-methods": "7.24.1", "@babel/plugin-transform-private-property-in-object": "7.24.1", "@babel/plugin-transform-runtime": "7.24.3", "@babel/preset-env": "7.24.3", "@babel/preset-react": "7.24.1", "@babel/preset-typescript": "7.24.1", "@commitlint/cli": "19.2.1", "@commitlint/config-conventional": "19.1.0", "@commitlint/cz-commitlint": "19.2.0", "@emotion/eslint-plugin": "11.11.0", "@emotion/react": "11.11.4", "@emotion/styled": "11.11.0", "@jest/environment": "29.7.0", "@release-it/conventional-changelog": "8.0.1", "@rollup/plugin-babel": "6.0.4", "@rollup/plugin-commonjs": "25.0.7", "@rollup/plugin-json": "6.1.0", "@rollup/plugin-node-resolve": "15.2.3", "@rollup/plugin-replace": "5.0.5", "@rollup/plugin-strip": "3.0.4", "@rollup/plugin-terser": "0.4.4", "@storybook/addon-docs": "8.0.4", "@storybook/addon-essentials": "8.0.4", "@storybook/addon-storysource": "8.0.4", "@storybook/addon-webpack5-compiler-swc": "1.0.2", "@storybook/manager-api": "8.0.4", "@storybook/react": "8.0.4", "@storybook/react-webpack5": "8.0.4", "@storybook/theming": "8.0.4", "@swc/core": "1.4.8", "@testing-library/dom": "9.3.4", "@testing-library/jest-dom": "6.4.2", "@testing-library/react": "14.2.2", "@testing-library/react-16-17": "npm:@testing-library/react@12.1.5", "@types/express": "4.17.21", "@types/fs-extra": "11.0.4", "@types/jest": "29.5.12", "@types/jest-axe": "3.5.9", "@types/jsdom": "21.1.6", "@types/markdown-it": "13.0.7", "@types/node": "20.11.30", "@types/raf-schd": "4.0.3", "@types/react": "18.2.69", "@types/react-dom": "18.2.22", "@types/react-redux": "7.1.33", "@types/react-virtualized": "9.21.29", "@types/react-window": "1.8.8", "@types/seedrandom": "3.0.8", "@typescript-eslint/eslint-plugin": "7.3.1", "@typescript-eslint/parser": "7.3.1", "babel-jest": "29.7.0", "babel-loader": "9.1.3", "babel-plugin-dev-expression": "0.2.3", "babel-plugin-module-resolver": "5.0.0", "commitizen": "4.3.0", "cross-env": "7.0.3", "csstype": "3.1.3", "cypress": "13.7.1", "dotenv": "16.4.5", "eslint": "8.57.0", "eslint-config-airbnb": "19.0.4", "eslint-config-prettier": "9.1.0", "eslint-import-resolver-typescript": "3.6.1", "eslint-plugin-cypress": "2.15.1", "eslint-plugin-es5": "1.5.0", "eslint-plugin-import": "2.29.1", "eslint-plugin-jest": "27.9.0", "eslint-plugin-jsx-a11y": "6.8.0", "eslint-plugin-node": "11.1.0", "eslint-plugin-prettier": "5.1.3", "eslint-plugin-react": "7.34.1", "eslint-plugin-react-hooks": "4.6.0", "eslint-plugin-storybook": "0.8.0", "express": "4.19.1", "fast-glob": "3.3.2", "fs-extra": "11.2.0", "husky": "9.0.11", "jest": "29.7.0", "jest-axe": "8.0.0", "jest-environment-jsdom": "29.7.0", "jest-junit": "16.0.0", "jest-watch-typeahead": "2.2.2", "jsdom": "24.0.0", "lighthouse": "11.7.0", "markdown-it": "14.1.0", "memory-fs": "0.5.0", "postcss-styled-syntax": "0.6.4", "prettier": "3.2.5", "raf-stub": "3.0.0", "react": "18.2.0", "react-16": "npm:react@16.14.0", "react-17": "npm:react@17.0.2", "react-dom": "18.2.0", "react-dom-16": "npm:react-dom@16.14.0", "react-dom-17": "npm:react-dom@17.0.2", "react-virtualized": "9.22.5", "react-window": "1.8.10", "release-it": "17.1.1", "require-from-string": "2.0.2", "rimraf": "5.0.5", "rollup": "4.13.0", "rollup-plugin-dts": "6.1.0", "seedrandom": "3.0.5", "storybook": "8.0.4", "styled-components": "6.1.8", "stylelint": "16.3.0", "stylelint-config-recommended": "14.0.0", "stylelint-config-standard": "36.0.0", "ts-node": "^10.9.2", "typescript": "5.4.3", "wait-on": "7.2.0", "webpack": "5.91.0"}, "peerDependencies": {"react": "^16.8.5 || ^17.0.0 || ^18.0.0", "react-dom": "^16.8.5 || ^17.0.0 || ^18.0.0"}, "license": "Apache-2.0", "jest-junit": {"output": "test-reports/junit/js-test-results.xml"}, "packageManager": "pnpm@8.15.4"}