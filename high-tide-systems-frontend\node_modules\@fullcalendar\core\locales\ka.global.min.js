/*!
FullCalendar Core v6.1.17
Docs & License: https://fullcalendar.io
(c) 2024 Adam Shaw
*/
!function(e){"use strict";var t={code:"ka",week:{dow:1,doy:7},buttonText:{prev:"წინა",next:"შემდეგი",today:"დღეს",year:"წელიწადი",month:"თვე",week:"კვირა",day:"დღე",list:"დღის წესრიგი"},weekText:"კვ",allDayText:"მთელი დღე",moreLinkText:e=>"+ კიდევ "+e,noEventsText:"ღონისძიებები არ არის"};FullCalendar.globalLocales.push(t)}();