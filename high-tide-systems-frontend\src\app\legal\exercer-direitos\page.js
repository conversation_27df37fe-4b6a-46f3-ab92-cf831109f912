'use client';

import { useState } from 'react';
import { Calendar, User, Mail, Phone, FileText, Send, CheckCircle, AlertCircle } from 'lucide-react';

export default function ExercerDireitos() {
  const [formData, setFormData] = useState({
    nome: '',
    email: '',
    telefone: '',
    cpf: '',
    tipoSolicitacao: '',
    descricao: '',
    documentos: null
  });
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [submitted, setSubmitted] = useState(false);

  const handleInputChange = (e) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));
  };

  const handleFileChange = (e) => {
    setFormData(prev => ({
      ...prev,
      documentos: e.target.files[0]
    }));
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    setIsSubmitting(true);
    
    // Simular envio do formulário
    setTimeout(() => {
      setIsSubmitting(false);
      setSubmitted(true);
    }, 2000);
  };

  if (submitted) {
    return (
      <div className="p-8">
        <div className="max-w-2xl mx-auto text-center">
          <div className="bg-green-50 dark:bg-green-900/20 p-8 rounded-lg">
            <CheckCircle className="mx-auto text-green-600 mb-4" size={64} />
            <h1 className="text-2xl font-bold text-green-900 dark:text-green-100 mb-4">
              Solicitação Enviada com Sucesso!
            </h1>
            <p className="text-green-800 dark:text-green-200 mb-6">
              Sua solicitação foi recebida e será processada em até 15 dias úteis, conforme estabelecido pela LGPD. 
              Você receberá uma confirmação por e-mail em breve.
            </p>
            <div className="bg-white dark:bg-gray-800 p-4 rounded-lg mb-6">
              <p className="text-sm text-gray-600 dark:text-gray-400">
                <strong>Protocolo:</strong> LGPD-{Date.now().toString().slice(-8)}
              </p>
            </div>
            <button 
              onClick={() => {
                setSubmitted(false);
                setFormData({
                  nome: '',
                  email: '',
                  telefone: '',
                  cpf: '',
                  tipoSolicitacao: '',
                  descricao: '',
                  documentos: null
                });
              }}
              className="px-6 py-3 bg-primary-600 text-white rounded-md hover:bg-primary-700 transition-colors"
            >
              Nova Solicitação
            </button>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="p-8">
      <div className="max-w-4xl mx-auto">
        {/* Header */}
        <div className="mb-8">
          <h1 className="text-3xl font-bold text-gray-900 dark:text-white mb-4">
            Exercer Direitos LGPD
          </h1>
          <div className="flex items-center space-x-4 text-sm text-gray-600 dark:text-gray-400">
            <div className="flex items-center space-x-1">
              <Calendar size={16} />
              <span>Resposta em até 15 dias úteis</span>
            </div>
            <div className="flex items-center space-x-1">
              <span className="w-2 h-2 bg-green-500 rounded-full"></span>
              <span>Gratuito</span>
            </div>
          </div>
        </div>

        {/* Introduction */}
        <section className="mb-8">
          <div className="bg-primary-50 dark:bg-primary-900/20 p-6 rounded-lg mb-6">
            <h2 className="text-xl font-semibold text-primary-900 dark:text-primary-100 mb-3">
              Seus Direitos como Titular de Dados
            </h2>
            <p className="text-primary-800 dark:text-primary-200">
              Conforme a Lei Geral de Proteção de Dados (LGPD), você possui direitos específicos em relação 
              aos seus dados pessoais. Use este formulário para exercer qualquer um desses direitos de forma 
              simples e gratuita.
            </p>
          </div>
        </section>

        {/* Rights Overview */}
        <section className="mb-8">
          <h2 className="text-2xl font-semibold text-gray-900 dark:text-white mb-4">
            Direitos Disponíveis
          </h2>
          <div className="grid md:grid-cols-2 gap-4">
            <div className="border border-primary-200 dark:border-primary-700 p-4 rounded-lg">
              <h4 className="font-semibold text-primary-900 dark:text-primary-100 mb-2">📋 Confirmação e Acesso</h4>
              <p className="text-gray-700 dark:text-gray-300 text-sm">
                Confirmar se tratamos seus dados e obter acesso a eles.
              </p>
            </div>
            <div className="border border-primary-200 dark:border-primary-700 p-4 rounded-lg">
              <h4 className="font-semibold text-primary-900 dark:text-primary-100 mb-2">✏️ Correção</h4>
              <p className="text-gray-700 dark:text-gray-300 text-sm">
                Corrigir dados incompletos, inexatos ou desatualizados.
              </p>
            </div>
            <div className="border border-primary-200 dark:border-primary-700 p-4 rounded-lg">
              <h4 className="font-semibold text-primary-900 dark:text-primary-100 mb-2">🗑️ Exclusão</h4>
              <p className="text-gray-700 dark:text-gray-300 text-sm">
                Solicitar a exclusão de dados desnecessários ou tratados indevidamente.
              </p>
            </div>
            <div className="border border-primary-200 dark:border-primary-700 p-4 rounded-lg">
              <h4 className="font-semibold text-primary-900 dark:text-primary-100 mb-2">📤 Portabilidade</h4>
              <p className="text-gray-700 dark:text-gray-300 text-sm">
                Obter seus dados em formato estruturado para outro fornecedor.
              </p>
            </div>
            <div className="border border-primary-200 dark:border-primary-700 p-4 rounded-lg">
              <h4 className="font-semibold text-primary-900 dark:text-primary-100 mb-2">🚫 Oposição</h4>
              <p className="text-gray-700 dark:text-gray-300 text-sm">
                Opor-se ao tratamento realizado com base no legítimo interesse.
              </p>
            </div>
            <div className="border border-primary-200 dark:border-primary-700 p-4 rounded-lg">
              <h4 className="font-semibold text-primary-900 dark:text-primary-100 mb-2">❌ Revogação</h4>
              <p className="text-gray-700 dark:text-gray-300 text-sm">
                Revogar o consentimento dado para tratamento de dados.
              </p>
            </div>
          </div>
        </section>

        {/* Form */}
        <section className="mb-8">
          <div className="bg-white dark:bg-gray-800 p-6 rounded-lg shadow-sm">
            <h2 className="text-2xl font-semibold text-gray-900 dark:text-white mb-6">
              Formulário de Solicitação
            </h2>
            
            <form onSubmit={handleSubmit} className="space-y-6">
              {/* Personal Information */}
              <div className="grid md:grid-cols-2 gap-6">
                <div>
                  <label htmlFor="nome" className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                    Nome Completo *
                  </label>
                  <div className="relative">
                    <User className="absolute left-3 top-3 text-gray-400" size={18} />
                    <input
                      type="text"
                      id="nome"
                      name="nome"
                      value={formData.nome}
                      onChange={handleInputChange}
                      required
                      className="w-full pl-10 pr-4 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:ring-2 focus:ring-primary-500 focus:border-primary-500 dark:bg-gray-700 dark:text-white"
                      placeholder="Seu nome completo"
                    />
                  </div>
                </div>
                
                <div>
                  <label htmlFor="email" className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                    E-mail *
                  </label>
                  <div className="relative">
                    <Mail className="absolute left-3 top-3 text-gray-400" size={18} />
                    <input
                      type="email"
                      id="email"
                      name="email"
                      value={formData.email}
                      onChange={handleInputChange}
                      required
                      className="w-full pl-10 pr-4 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:ring-2 focus:ring-primary-500 focus:border-primary-500 dark:bg-gray-700 dark:text-white"
                      placeholder="<EMAIL>"
                    />
                  </div>
                </div>
              </div>

              <div className="grid md:grid-cols-2 gap-6">
                <div>
                  <label htmlFor="telefone" className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                    Telefone
                  </label>
                  <div className="relative">
                    <Phone className="absolute left-3 top-3 text-gray-400" size={18} />
                    <input
                      type="tel"
                      id="telefone"
                      name="telefone"
                      value={formData.telefone}
                      onChange={handleInputChange}
                      className="w-full pl-10 pr-4 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:ring-2 focus:ring-primary-500 focus:border-primary-500 dark:bg-gray-700 dark:text-white"
                      placeholder="(11) 99999-9999"
                    />
                  </div>
                </div>
                
                <div>
                  <label htmlFor="cpf" className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                    CPF *
                  </label>
                  <input
                    type="text"
                    id="cpf"
                    name="cpf"
                    value={formData.cpf}
                    onChange={handleInputChange}
                    required
                    className="w-full px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:ring-2 focus:ring-primary-500 focus:border-primary-500 dark:bg-gray-700 dark:text-white"
                    placeholder="000.000.000-00"
                  />
                </div>
              </div>

              {/* Request Type */}
              <div>
                <label htmlFor="tipoSolicitacao" className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                  Tipo de Solicitação *
                </label>
                <select
                  id="tipoSolicitacao"
                  name="tipoSolicitacao"
                  value={formData.tipoSolicitacao}
                  onChange={handleInputChange}
                  required
                  className="w-full px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:ring-2 focus:ring-primary-500 focus:border-primary-500 dark:bg-gray-700 dark:text-white"
                >
                  <option value="">Selecione o tipo de solicitação</option>
                  <option value="confirmacao">Confirmação da existência de tratamento</option>
                  <option value="acesso">Acesso aos dados pessoais</option>
                  <option value="correcao">Correção de dados incompletos/inexatos</option>
                  <option value="exclusao">Exclusão de dados</option>
                  <option value="portabilidade">Portabilidade dos dados</option>
                  <option value="oposicao">Oposição ao tratamento</option>
                  <option value="revogacao">Revogação do consentimento</option>
                  <option value="informacoes">Informações sobre compartilhamento</option>
                  <option value="outro">Outro</option>
                </select>
              </div>

              {/* Description */}
              <div>
                <label htmlFor="descricao" className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                  Descrição da Solicitação *
                </label>
                <textarea
                  id="descricao"
                  name="descricao"
                  value={formData.descricao}
                  onChange={handleInputChange}
                  required
                  rows={4}
                  className="w-full px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:ring-2 focus:ring-primary-500 focus:border-primary-500 dark:bg-gray-700 dark:text-white"
                  placeholder="Descreva detalhadamente sua solicitação..."
                />
              </div>

              {/* File Upload */}
              <div>
                <label htmlFor="documentos" className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                  Documentos Comprobatórios
                </label>
                <div className="relative">
                  <FileText className="absolute left-3 top-3 text-gray-400" size={18} />
                  <input
                    type="file"
                    id="documentos"
                    name="documentos"
                    onChange={handleFileChange}
                    accept=".pdf,.jpg,.jpeg,.png,.doc,.docx"
                    className="w-full pl-10 pr-4 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:ring-2 focus:ring-primary-500 focus:border-primary-500 dark:bg-gray-700 dark:text-white"
                  />
                </div>
                <p className="text-xs text-gray-500 dark:text-gray-400 mt-1">
                  Anexe documentos que comprovem sua identidade (RG, CNH, etc.). Máximo 5MB.
                </p>
              </div>

              {/* Important Notice */}
              <div className="bg-yellow-50 dark:bg-yellow-900/20 p-4 rounded-lg">
                <div className="flex items-start space-x-3">
                  <AlertCircle className="text-yellow-600 mt-1" size={20} />
                  <div>
                    <h4 className="font-semibold text-yellow-900 dark:text-yellow-100 mb-2">
                      Informações Importantes
                    </h4>
                    <ul className="list-disc list-inside space-y-1 text-yellow-800 dark:text-yellow-200 text-sm">
                      <li>Responderemos em até 15 dias úteis conforme a LGPD</li>
                      <li>Pode ser necessário comprovar sua identidade</li>
                      <li>Algumas solicitações podem ter limitações legais</li>
                      <li>Você receberá confirmação por e-mail</li>
                    </ul>
                  </div>
                </div>
              </div>

              {/* Submit Button */}
              <div className="flex justify-end">
                <button
                  type="submit"
                  disabled={isSubmitting}
                  className="inline-flex items-center px-6 py-3 bg-primary-600 text-white rounded-md hover:bg-primary-700 focus:ring-2 focus:ring-primary-500 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
                >
                  {isSubmitting ? (
                    <>
                      <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                      Enviando...
                    </>
                  ) : (
                    <>
                      <Send className="mr-2" size={18} />
                      Enviar Solicitação
                    </>
                  )}
                </button>
              </div>
            </form>
          </div>
        </section>

        {/* Contact Information */}
        <section className="mb-8">
          <div className="bg-gray-50 dark:bg-gray-700 p-6 rounded-lg">
            <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-4">
              Outras Formas de Contato
            </h3>
            <div className="grid md:grid-cols-2 gap-6">
              <div>
                <h4 className="font-semibold text-gray-900 dark:text-white mb-2">Encarregado de Dados (DPO)</h4>
                <div className="space-y-2 text-sm text-gray-700 dark:text-gray-300">
                  <div className="flex items-center space-x-2">
                    <Mail size={16} />
                    <span><EMAIL></span>
                  </div>
                  <div className="flex items-center space-x-2">
                    <Phone size={16} />
                    <span>+55 (11) 99999-9999</span>
                  </div>
                </div>
              </div>
              
              <div>
                <h4 className="font-semibold text-gray-900 dark:text-white mb-2">Suporte Geral</h4>
                <div className="space-y-2 text-sm text-gray-700 dark:text-gray-300">
                  <div className="flex items-center space-x-2">
                    <Mail size={16} />
                    <span><EMAIL></span>
                  </div>
                  <p className="text-xs text-gray-500 dark:text-gray-400">
                    Horário de atendimento: Segunda a Sexta, 9h às 18h
                  </p>
                </div>
              </div>
            </div>
          </div>
        </section>
      </div>
    </div>
  );
}
